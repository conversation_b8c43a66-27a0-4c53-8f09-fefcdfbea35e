{"name": "cleaning-service-backend", "version": "1.0.0", "description": "Backend API for Cleaning Service Application", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "server": "node src/server.js", "setup": "node scripts/setup.js", "seed": "node -e \"require('./src/database/seeders').runAll()\"", "seed:users": "node -e \"require('./src/database/seeders').runSeeder('users')\"", "seed:services": "node -e \"require('./src/database/seeders').runSeeder('services')\"", "seed:testimonials": "node -e \"require('./src/database/seeders').runSeeder('testimonials')\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "lint": "eslint src/ tests/", "lint:fix": "eslint src/ tests/ --fix", "clean": "rm -rf coverage node_modules/.cache"}, "keywords": ["cleaning", "service", "booking", "api", "express", "mongodb"], "author": "Cleaning Service Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^8.0.3", "mongoose-paginate-v2": "^1.7.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "twilio": "^5.7.0", "uuid": "^9.0.1"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}