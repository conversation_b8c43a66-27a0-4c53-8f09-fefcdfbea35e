#!/usr/bin/env node

/**
 * Validation script to check if the new backend structure is properly set up
 */

const fs = require('fs');
const path = require('path');

class StructureValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.success = [];
  }

  /**
   * Check if a file or directory exists
   */
  checkExists(filePath, description, required = true) {
    const fullPath = path.join(__dirname, '..', filePath);
    
    if (fs.existsSync(fullPath)) {
      this.success.push(`✅ ${description}: ${filePath}`);
      return true;
    } else {
      const message = `❌ Missing ${description}: ${filePath}`;
      if (required) {
        this.errors.push(message);
      } else {
        this.warnings.push(message);
      }
      return false;
    }
  }

  /**
   * Check if a file contains specific content
   */
  checkFileContent(filePath, searchString, description) {
    const fullPath = path.join(__dirname, '..', filePath);
    
    if (!fs.existsSync(fullPath)) {
      this.errors.push(`❌ Cannot check content - file missing: ${filePath}`);
      return false;
    }

    try {
      const content = fs.readFileSync(fullPath, 'utf8');
      if (content.includes(searchString)) {
        this.success.push(`✅ ${description} in ${filePath}`);
        return true;
      } else {
        this.warnings.push(`⚠️  ${description} not found in ${filePath}`);
        return false;
      }
    } catch (error) {
      this.errors.push(`❌ Error reading ${filePath}: ${error.message}`);
      return false;
    }
  }

  /**
   * Validate the entire structure
   */
  validate() {
    console.log('🔍 Validating Backend Structure...\n');

    // Check main entry points
    this.checkExists('index.js', 'Main entry point');
    this.checkExists('package.json', 'Package configuration');
    this.checkExists('jest.config.js', 'Jest configuration');
    this.checkExists('.env.example', 'Environment template');

    // Check source structure
    this.checkExists('src/app.js', 'Express app configuration');
    this.checkExists('src/server.js', 'Server startup logic');

    // Check configuration
    this.checkExists('src/config/index.js', 'Main configuration');
    this.checkExists('src/config/environments/development.js', 'Development config');
    this.checkExists('src/config/environments/production.js', 'Production config');
    this.checkExists('src/config/environments/test.js', 'Test config');

    // Check database layer
    this.checkExists('src/database/connection.js', 'Database connection');
    this.checkExists('src/database/index.js', 'Database exports');
    this.checkExists('src/database/seeders/index.js', 'Database seeders');

    // Check models
    this.checkExists('src/models/index.js', 'Models exports');
    this.checkExists('src/models/User.js', 'User model');

    // Check services
    this.checkExists('src/services/index.js', 'Services exports');
    this.checkExists('src/services/baseService.js', 'Base service');
    this.checkExists('src/services/authService.js', 'Auth service');
    this.checkExists('src/services/userService.js', 'User service');

    // Check middleware
    this.checkExists('src/middleware/index.js', 'Middleware exports');
    this.checkExists('src/middleware/auth/index.js', 'Auth middleware exports');
    this.checkExists('src/middleware/auth/auth.js', 'Auth middleware');
    this.checkExists('src/middleware/security/index.js', 'Security middleware exports');
    this.checkExists('src/middleware/validation/index.js', 'Validation middleware exports');

    // Check routes
    this.checkExists('src/routes/index.js', 'Routes exports');
    this.checkExists('src/routes/auth.js', 'Auth routes');

    // Check controllers
    this.checkExists('src/controllers/authController.js', 'Auth controller');

    // Check utilities
    this.checkExists('src/utils/index.js', 'Utils exports');
    this.checkExists('src/helpers/index.js', 'Helpers exports');
    this.checkExists('src/constants/index.js', 'Constants exports');

    // Check testing structure
    this.checkExists('tests/setup.js', 'Test setup');
    this.checkExists('tests/utils/testHelpers.js', 'Test helpers');
    this.checkExists('tests/fixtures/users.js', 'Test fixtures');
    this.checkExists('tests/unit/services/authService.test.js', 'Unit tests');
    this.checkExists('tests/integration/auth.test.js', 'Integration tests');

    // Check scripts
    this.checkExists('scripts/setup.js', 'Setup script');
    this.checkExists('scripts/validate-structure.js', 'Validation script');

    // Check documentation
    this.checkExists('docs/FOLDER_STRUCTURE.md', 'Structure documentation');
    this.checkExists('docs/MIGRATION_GUIDE.md', 'Migration guide');

    // Check package.json content
    this.checkFileContent('package.json', '"main": "index.js"', 'Updated main entry point');
    this.checkFileContent('package.json', '"seed":', 'Seed scripts');
    this.checkFileContent('package.json', '"test:coverage":', 'Test coverage script');

    // Check if old files are removed/moved
    this.checkOldFiles();

    // Display results
    this.displayResults();
  }

  /**
   * Check if old files are properly removed or moved
   */
  checkOldFiles() {
    const oldFiles = [
      'start.js',
      'setup.js',
      'seed-testimonials.js',
      'test-contact-api.js',
      'test-sms.js'
    ];

    oldFiles.forEach(file => {
      const fullPath = path.join(__dirname, '..', file);
      if (fs.existsSync(fullPath)) {
        this.warnings.push(`⚠️  Old file still exists: ${file} (should be moved to scripts/)`);
      } else {
        this.success.push(`✅ Old file properly moved: ${file}`);
      }
    });
  }

  /**
   * Display validation results
   */
  displayResults() {
    console.log('\n📊 Validation Results:\n');

    if (this.success.length > 0) {
      console.log('✅ SUCCESS:');
      this.success.forEach(msg => console.log(`  ${msg}`));
      console.log('');
    }

    if (this.warnings.length > 0) {
      console.log('⚠️  WARNINGS:');
      this.warnings.forEach(msg => console.log(`  ${msg}`));
      console.log('');
    }

    if (this.errors.length > 0) {
      console.log('❌ ERRORS:');
      this.errors.forEach(msg => console.log(`  ${msg}`));
      console.log('');
    }

    // Summary
    console.log('📈 SUMMARY:');
    console.log(`  ✅ Success: ${this.success.length}`);
    console.log(`  ⚠️  Warnings: ${this.warnings.length}`);
    console.log(`  ❌ Errors: ${this.errors.length}`);

    if (this.errors.length === 0) {
      console.log('\n🎉 Backend structure validation passed!');
      console.log('Your backend is properly organized and ready for development.');
    } else {
      console.log('\n🔧 Please fix the errors above before proceeding.');
      process.exit(1);
    }
  }
}

// Run validation
const validator = new StructureValidator();
validator.validate();
