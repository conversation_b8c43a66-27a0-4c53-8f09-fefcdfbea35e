#!/usr/bin/env node

/**
 * Main entry point for the Cleaning Service Backend API
 * This file handles environment setup and starts the server
 */

const path = require('path');
const fs = require('fs');

// Ensure .env file exists
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.log('⚠️  .env file not found. Creating from .env.example...');
  const examplePath = path.join(__dirname, '.env.example');
  
  if (fs.existsSync(examplePath)) {
    fs.copyFileSync(examplePath, envPath);
    console.log('✅ .env file created. Please update it with your configuration.\n');
  } else {
    console.log('❌ .env.example file not found. Please create .env manually.\n');
    console.log('Required environment variables:');
    console.log('- MONGODB_URI');
    console.log('- JWT_SECRET');
    console.log('- NODE_ENV');
    console.log('- PORT');
    process.exit(1);
  }
}

// Load environment variables
require('dotenv').config();

// Validate required environment variables
const requiredEnvVars = ['MONGODB_URI', 'JWT_SECRET'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingEnvVars.forEach(envVar => console.error(`  - ${envVar}`));
  process.exit(1);
}

// Start the server
console.log('🚀 Starting Cleaning Service Backend...\n');

const Server = require('./src/server');
const server = new Server();

// Start the server
server.start().catch(error => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});
