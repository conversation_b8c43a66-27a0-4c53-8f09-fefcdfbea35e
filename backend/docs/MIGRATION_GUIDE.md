# Migration Guide: Backend Restructuring

This guide explains how to migrate from the old backend structure to the new professional structure.

## 🔄 What Changed

### File Movements

| Old Location | New Location | Notes |
|-------------|-------------|-------|
| `start.js` | `index.js` | Main entry point renamed |
| `src/server.js` | `src/app.js` + `src/server.js` | Separated concerns |
| `src/config/database.js` | `src/database/connection.js` | Better organization |
| `setup.js` | `scripts/setup.js` | Moved to scripts directory |
| `test-*.js` | `scripts/test-*.js` | Moved to scripts directory |
| `src/middleware/auth.js` | `src/middleware/auth/auth.js` | Categorized middleware |
| `src/middleware/validation.js` | `src/middleware/validation/validation.js` | Categorized middleware |

### New Additions

- `src/services/` - Business logic layer
- `src/constants/` - Application constants
- `src/helpers/` - Utility functions
- `src/config/environments/` - Environment-specific configs
- `src/database/seeders/` - Database seeders
- `tests/` - Comprehensive testing structure
- `docs/` - Documentation

## 📋 Step-by-Step Migration

### 1. Update Package.json Scripts

**Before:**
```json
{
  "main": "src/server.js",
  "scripts": {
    "start": "node start.js",
    "dev": "nodemon src/server.js",
    "setup": "node setup.js"
  }
}
```

**After:**
```json
{
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "dev": "nodemon index.js",
    "setup": "node scripts/setup.js",
    "seed": "node -e \"require('./src/database/seeders').runAll()\"",
    "test": "jest",
    "test:coverage": "jest --coverage"
  }
}
```

### 2. Update Import Statements

**Controllers (Before):**
```javascript
const asyncHandler = require('../utils/asyncHandler');
const ErrorResponse = require('../utils/errorResponse');
const User = require('../models/User');
```

**Controllers (After):**
```javascript
const { authService } = require('../services');
const { asyncHandler } = require('../middleware');
const { HTTP_STATUS } = require('../constants');
```

### 3. Refactor Controllers to Use Services

**Before (Controller with business logic):**
```javascript
exports.register = asyncHandler(async (req, res, next) => {
  const { name, email, phone, password } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({ email });
  if (existingUser) {
    return next(new ErrorResponse('User already exists', 400));
  }

  // Create user
  const user = await User.create({ name, email, phone, password });
  
  // Generate token
  const token = user.getSignedJwtToken();
  
  res.status(201).json({ success: true, token, data: user });
});
```

**After (Controller using service):**
```javascript
exports.register = asyncHandler(async (req, res, next) => {
  const result = await authService.register(req.body);
  
  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    token: result.tokens.accessToken,
    data: result.user
  });
});
```

### 4. Update Middleware Imports

**Before:**
```javascript
const { protect, authorize } = require('../middleware/auth');
const { validateUserRegistration } = require('../middleware/validation');
```

**After:**
```javascript
const { protect, authorize, validateUserRegistration } = require('../middleware');
```

### 5. Update Configuration Usage

**Before:**
```javascript
require('dotenv').config();
const mongoURI = process.env.MONGODB_URI;
const jwtSecret = process.env.JWT_SECRET;
```

**After:**
```javascript
const config = require('../config');
const mongoURI = config.database.uri;
const jwtSecret = config.jwt.secret;
```

## 🧪 Testing Migration

### 1. Install Testing Dependencies
```bash
npm install --save-dev mongodb-memory-server supertest
```

### 2. Run Tests
```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test types
npm run test:unit
npm run test:integration
```

### 3. Verify Structure
```bash
# Check if server starts correctly
npm run dev

# Run database seeding
npm run seed

# Check linting
npm run lint
```

## ⚠️ Breaking Changes

### 1. Entry Point Changed
- Old: `node start.js`
- New: `node index.js`

### 2. Import Paths Changed
- Update all relative imports to use new structure
- Use centralized exports from index files

### 3. Configuration Access
- Replace direct `process.env` usage with config object
- Use environment-specific configurations

### 4. Middleware Organization
- Update middleware imports to use categorized structure
- Use centralized middleware exports

## 🔧 Troubleshooting

### Common Issues

1. **Module Not Found Errors**
   - Check import paths are updated to new structure
   - Ensure index.js files are created for directories

2. **Configuration Errors**
   - Verify .env file exists and has required variables
   - Check config/index.js for proper environment variable mapping

3. **Database Connection Issues**
   - Update database connection imports
   - Use new database connection class

4. **Test Failures**
   - Update test imports to use new structure
   - Ensure test setup files are properly configured

### Verification Checklist

- [ ] Server starts without errors
- [ ] All API endpoints work correctly
- [ ] Database connection is successful
- [ ] Tests pass
- [ ] Linting passes
- [ ] Environment variables are properly loaded
- [ ] Middleware functions correctly
- [ ] Services contain business logic
- [ ] Controllers are thin and focused

## 📞 Support

If you encounter issues during migration:

1. Check the error logs for specific import/path issues
2. Verify all files are in their new locations
3. Ensure package.json scripts are updated
4. Run tests to verify functionality
5. Check the FOLDER_STRUCTURE.md for reference

## 🎉 Benefits After Migration

- ✅ Clean, maintainable code structure
- ✅ Proper separation of concerns
- ✅ Comprehensive testing infrastructure
- ✅ Professional development practices
- ✅ Scalable architecture
- ✅ Better error handling
- ✅ Centralized configuration management
