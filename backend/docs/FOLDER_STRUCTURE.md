# Backend Folder Structure

This document explains the professional folder structure implemented for the Cleaning Service Backend API.

## 📁 Directory Structure

```
backend/
├── index.js                    # Main entry point
├── package.json                # Dependencies and scripts
├── jest.config.js             # Jest testing configuration
├── .env.example               # Environment variables template
├── README.md                  # Project documentation
├── docs/                      # Documentation files
│   ├── FOLDER_STRUCTURE.md    # This file
│   └── API.md                 # API documentation
├── scripts/                   # Utility scripts
│   ├── setup.js              # Database setup script
│   ├── seed-testimonials.js  # Testimonials seeder
│   └── test-*.js             # Testing scripts
├── src/                       # Source code
│   ├── app.js                # Express app configuration
│   ├── server.js             # Server startup logic
│   ├── config/               # Configuration files
│   │   ├── index.js          # Main configuration
│   │   └── environments/     # Environment-specific configs
│   │       ├── development.js
│   │       ├── production.js
│   │       └── test.js
│   ├── constants/            # Application constants
│   │   └── index.js          # Constants and enums
│   ├── controllers/          # Route controllers
│   │   ├── authController.js
│   │   ├── userController.js
│   │   └── ...
│   ├── database/             # Database layer
│   │   ├── connection.js     # Database connection
│   │   ├── index.js          # Database exports
│   │   ├── migrations/       # Database migrations
│   │   └── seeders/          # Database seeders
│   │       └── index.js
│   ├── helpers/              # Utility functions
│   │   └── index.js          # Helper functions
│   ├── middleware/           # Express middleware
│   │   ├── index.js          # Middleware exports
│   │   ├── auth/             # Authentication middleware
│   │   │   ├── index.js
│   │   │   └── auth.js
│   │   ├── security/         # Security middleware
│   │   │   ├── index.js
│   │   │   ├── rateLimiter.js
│   │   │   └── sensitiveOperationLimit.js
│   │   ├── validation/       # Validation middleware
│   │   │   ├── index.js
│   │   │   └── validation.js
│   │   ├── asyncHandler.js   # Async error handler
│   │   ├── errorHandler.js   # Global error handler
│   │   └── notFound.js       # 404 handler
│   ├── models/               # Database models
│   │   ├── index.js          # Model exports
│   │   ├── User.js
│   │   ├── Booking.js
│   │   └── ...
│   ├── routes/               # API routes
│   │   ├── index.js          # Route exports
│   │   ├── auth.js
│   │   ├── users.js
│   │   └── ...
│   ├── services/             # Business logic layer
│   │   ├── index.js          # Service exports
│   │   ├── baseService.js    # Base service class
│   │   ├── authService.js
│   │   ├── userService.js
│   │   └── ...
│   └── utils/                # Utility modules
│       ├── index.js          # Utility exports
│       ├── asyncHandler.js   # Async wrapper
│       ├── errorResponse.js  # Error response class
│       ├── email.js          # Email utilities
│       └── sms.js            # SMS utilities
└── tests/                    # Test files
    ├── setup.js              # Test setup
    ├── fixtures/             # Test data fixtures
    │   └── users.js
    ├── utils/                # Test utilities
    │   └── testHelpers.js
    ├── unit/                 # Unit tests
    │   └── services/
    │       └── authService.test.js
    └── integration/          # Integration tests
        └── auth.test.js
```

## 🏗️ Architecture Principles

### 1. Separation of Concerns
- **Controllers**: Handle HTTP requests/responses only
- **Services**: Contain business logic
- **Models**: Define data structure and validation
- **Middleware**: Handle cross-cutting concerns
- **Utils**: Provide reusable utility functions

### 2. Layered Architecture
```
Routes → Controllers → Services → Models → Database
```

### 3. Dependency Injection
- Services are injected into controllers
- Models are injected into services
- Configuration is centralized and injected

## 📋 Key Improvements

### Before (Issues)
- ❌ Business logic mixed in controllers
- ❌ No centralized configuration
- ❌ Poor middleware organization
- ❌ Scattered utility functions
- ❌ No proper testing structure
- ❌ Setup scripts in root directory

### After (Solutions)
- ✅ Clean separation of concerns
- ✅ Centralized configuration with environment-specific settings
- ✅ Organized middleware by category
- ✅ Structured utility functions and helpers
- ✅ Professional testing infrastructure
- ✅ Organized scripts directory

## 🚀 Benefits

1. **Maintainability**: Clear structure makes code easier to maintain
2. **Scalability**: Easy to add new features without breaking existing code
3. **Testability**: Proper separation allows for comprehensive testing
4. **Team Collaboration**: Standard structure helps team members navigate code
5. **Code Reusability**: Services and utilities can be easily reused
6. **Error Handling**: Centralized error handling across the application

## 📝 Usage Examples

### Using Services in Controllers
```javascript
const { authService } = require('../services');

exports.login = asyncHandler(async (req, res, next) => {
  const { email, password } = req.body;
  
  const result = await authService.login(email, password);
  
  res.status(200).json({
    success: true,
    data: result.user,
    token: result.tokens.accessToken
  });
});
```

### Using Centralized Configuration
```javascript
const config = require('../config');

// Access configuration values
const dbUri = config.database.uri;
const jwtSecret = config.jwt.secret;
```

### Using Organized Middleware
```javascript
const { protect, authorize, validateUserRegistration } = require('../middleware');

router.post('/register', validateUserRegistration, register);
router.get('/admin', protect, authorize('admin'), getAdminData);
```

## 🔧 Migration Notes

1. **Entry Point**: Changed from `start.js` to `index.js`
2. **Server Structure**: Separated app configuration from server startup
3. **Import Paths**: Updated all import paths to use new structure
4. **Scripts**: Moved utility scripts to `scripts/` directory
5. **Tests**: Created comprehensive testing infrastructure

This structure follows industry best practices and provides a solid foundation for scaling the application.
