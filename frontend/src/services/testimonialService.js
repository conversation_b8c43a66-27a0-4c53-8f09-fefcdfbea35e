const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1';

class TestimonialService {
  constructor() {
    this.baseURL = `${API_BASE_URL}/testimonials`;
  }

  // Helper method to get auth headers
  getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    };
  }

  // Helper method to handle API responses
  async handleResponse(response) {
    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Network error' }));
      throw new Error(error.message || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // Public methods (no authentication required)

  /**
   * Get all approved testimonials for public display
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} API response with testimonials
   */
  async getTestimonials(params = {}) {
    try {
      const queryString = new URLSearchParams(params).toString();
      const url = queryString ? `${this.baseURL}?${queryString}` : this.baseURL;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching testimonials:', error);
      throw error;
    }
  }

  /**
   * Get featured testimonials
   * @param {number} limit - Number of testimonials to fetch
   * @returns {Promise<Object>} API response with featured testimonials
   */
  async getFeaturedTestimonials(limit = 5) {
    try {
      const response = await fetch(`${this.baseURL}/featured?limit=${limit}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching featured testimonials:', error);
      throw error;
    }
  }

  /**
   * Get testimonial statistics
   * @returns {Promise<Object>} API response with testimonial stats
   */
  async getTestimonialStats() {
    try {
      const response = await fetch(`${this.baseURL}/stats`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching testimonial stats:', error);
      throw error;
    }
  }

  /**
   * Create new testimonial (public submission)
   * @param {Object} testimonialData - Testimonial data
   * @returns {Promise<Object>} API response with created testimonial
   */
  async createPublicTestimonial(testimonialData) {
    try {
      const response = await fetch(`${this.baseURL}/public`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testimonialData)
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error creating public testimonial:', error);
      throw error;
    }
  }

  // Admin methods (authentication required)

  /**
   * Get all testimonials for admin management
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} API response with all testimonials
   */
  async getAllTestimonials(params = {}) {
    try {
      const queryString = new URLSearchParams(params).toString();
      const url = queryString ? `${this.baseURL}/admin?${queryString}` : `${this.baseURL}/admin`;

      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching all testimonials:', error);
      throw error;
    }
  }

  /**
   * Get single testimonial by ID
   * @param {string} id - Testimonial ID
   * @returns {Promise<Object>} API response with testimonial
   */
  async getTestimonialById(id) {
    try {
      const response = await fetch(`${this.baseURL}/${id}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching testimonial:', error);
      throw error;
    }
  }

  /**
   * Create new testimonial
   * @param {Object} testimonialData - Testimonial data
   * @returns {Promise<Object>} API response with created testimonial
   */
  async createTestimonial(testimonialData) {
    try {
      const response = await fetch(this.baseURL, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(testimonialData)
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error creating testimonial:', error);
      throw error;
    }
  }

  /**
   * Update testimonial
   * @param {string} id - Testimonial ID
   * @param {Object} testimonialData - Updated testimonial data
   * @returns {Promise<Object>} API response with updated testimonial
   */
  async updateTestimonial(id, testimonialData) {
    try {
      const response = await fetch(`${this.baseURL}/${id}`, {
        method: 'PATCH',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(testimonialData)
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error updating testimonial:', error);
      throw error;
    }
  }

  /**
   * Delete testimonial
   * @param {string} id - Testimonial ID
   * @returns {Promise<Object>} API response
   */
  async deleteTestimonial(id) {
    try {
      const response = await fetch(`${this.baseURL}/${id}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error deleting testimonial:', error);
      throw error;
    }
  }

  /**
   * Approve testimonial
   * @param {string} id - Testimonial ID
   * @returns {Promise<Object>} API response
   */
  async approveTestimonial(id) {
    try {
      const response = await fetch(`${this.baseURL}/${id}/approve`, {
        method: 'PATCH',
        headers: this.getAuthHeaders()
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error approving testimonial:', error);
      throw error;
    }
  }

  /**
   * Toggle featured status
   * @param {string} id - Testimonial ID
   * @returns {Promise<Object>} API response
   */
  async toggleFeatured(id) {
    try {
      const response = await fetch(`${this.baseURL}/${id}/featured`, {
        method: 'PATCH',
        headers: this.getAuthHeaders()
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error toggling featured status:', error);
      throw error;
    }
  }

  /**
   * Bulk update testimonials
   * @param {Array} ids - Array of testimonial IDs
   * @param {string} action - Bulk action to perform
   * @param {Object} data - Additional data for update action
   * @returns {Promise<Object>} API response
   */
  async bulkUpdateTestimonials(ids, action, data = null) {
    try {
      const response = await fetch(`${this.baseURL}/bulk`, {
        method: 'PATCH',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ ids, action, data })
      });

      return await this.handleResponse(response);
    } catch (error) {
      console.error('Error bulk updating testimonials:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const testimonialService = new TestimonialService();
export default testimonialService;

// Also export the class for testing purposes
export { TestimonialService };
