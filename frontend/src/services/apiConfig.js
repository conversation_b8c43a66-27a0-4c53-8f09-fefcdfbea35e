/**
 * API Configuration
 * Centralized configuration for API endpoints and requests
 */

// API Base URL - adjust based on environment
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api/v1';

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    REGISTER: `${API_BASE_URL}/auth/register`,
    LOGIN: `${API_BASE_URL}/auth/login`,
    LOGOUT: `${API_BASE_URL}/auth/logout`,
    ME: `${API_BASE_URL}/auth/me`,
    FORGOT_PASSWORD: `${API_BASE_URL}/auth/forgot-password`,
    RESET_PASSWORD: `${API_BASE_URL}/auth/reset-password`,
    UPDATE_PASSWORD: `${API_BASE_URL}/auth/update-password`,
    VERIFY_EMAIL: `${API_BASE_URL}/auth/verify-email`,
    RESEND_VERIFICATION: `${API_BASE_URL}/auth/resend-verification`,
  },
  
  // Bookings
  BOOKINGS: {
    CREATE: `${API_BASE_URL}/bookings`,
    LIST: `${API_BASE_URL}/bookings`,
    GET: (id) => `${API_BASE_URL}/bookings/${id}`,
    UPDATE: (id) => `${API_BASE_URL}/bookings/${id}`,
    DELETE: (id) => `${API_BASE_URL}/bookings/${id}`,
    STATS: `${API_BASE_URL}/bookings/stats`,
  },
  
  // Services
  SERVICES: {
    LIST: `${API_BASE_URL}/services`,
    GET: (id) => `${API_BASE_URL}/services/${id}`,
    CREATE: `${API_BASE_URL}/services`,
    UPDATE: (id) => `${API_BASE_URL}/services/${id}`,
    DELETE: (id) => `${API_BASE_URL}/services/${id}`,
    FEATURED: `${API_BASE_URL}/services/featured`,
    BY_CATEGORY: (category) => `${API_BASE_URL}/services/category/${category}`,
  },
  
  // Contact
  CONTACT: {
    SUBMIT: `${API_BASE_URL}/contact`,
    LIST: `${API_BASE_URL}/contact`,
    GET: (id) => `${API_BASE_URL}/contact/${id}`,
    UPDATE: (id) => `${API_BASE_URL}/contact/${id}`,
    DELETE: (id) => `${API_BASE_URL}/contact/${id}`,
    STATS: `${API_BASE_URL}/contact/stats`,
  },
  
  // Users
  USERS: {
    PROFILE: `${API_BASE_URL}/users/profile`,
    UPDATE_PROFILE: `${API_BASE_URL}/users/profile`,
    UPDATE_PREFERENCES: `${API_BASE_URL}/users/preferences`,
    DELETE_ACCOUNT: `${API_BASE_URL}/users/account`,
  },
  
  // Admin
  ADMIN: {
    DASHBOARD: `${API_BASE_URL}/admin/dashboard`,
    USERS: `${API_BASE_URL}/admin/users`,
    USER: (id) => `${API_BASE_URL}/admin/users/${id}`,
    UPDATE_USER_ROLE: (id) => `${API_BASE_URL}/admin/users/${id}/role`,
    DEACTIVATE_USER: (id) => `${API_BASE_URL}/admin/users/${id}/deactivate`,
    LOGS: `${API_BASE_URL}/admin/logs`,
    STAFF: `${API_BASE_URL}/admin/staff`,
    UPDATE_STAFF: (id) => `${API_BASE_URL}/admin/staff/${id}`,
    TEST_SMS: `${API_BASE_URL}/admin/test-sms`,
  },
  
  // Health Check
  HEALTH: `${API_BASE_URL.replace('/api/v1', '')}/health`,
};

/**
 * Default headers for API requests
 */
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
};

/**
 * Get authorization headers with token
 * @param {string} token - JWT token
 * @returns {Object} Headers object with authorization
 */
export const getAuthHeaders = (token) => ({
  ...DEFAULT_HEADERS,
  'Authorization': `Bearer ${token}`,
});

/**
 * Generic API request function
 * @param {string} url - API endpoint URL
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} API response data
 */
export const apiRequest = async (url, options = {}) => {
  try {
    const config = {
      headers: DEFAULT_HEADERS,
      ...options,
    };

    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    // Handle network errors
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error('Unable to connect to the server. Please check your internet connection.');
    }
    throw error;
  }
};

/**
 * API request with authentication
 * @param {string} url - API endpoint URL
 * @param {Object} options - Fetch options
 * @param {string} token - JWT token
 * @returns {Promise<Object>} API response data
 */
export const authenticatedRequest = async (url, options = {}, token) => {
  const config = {
    ...options,
    headers: {
      ...getAuthHeaders(token),
      ...options.headers,
    },
  };

  return apiRequest(url, config);
};

/**
 * Handle API errors consistently
 * @param {Error} error - Error object
 * @returns {Object} Formatted error object
 */
export const handleApiError = (error) => {
  console.error('API Error:', error);

  // Default error response
  let errorResponse = {
    success: false,
    message: 'An unexpected error occurred. Please try again.',
    errors: [],
  };

  // Handle different error types
  if (error.message) {
    errorResponse.message = error.message;
  }

  // Handle validation errors (if the error has a specific format)
  if (error.errors && Array.isArray(error.errors)) {
    errorResponse.errors = error.errors;
  }

  return errorResponse;
};

export default {
  API_ENDPOINTS,
  DEFAULT_HEADERS,
  getAuthHeaders,
  apiRequest,
  authenticatedRequest,
  handleApiError,
};
