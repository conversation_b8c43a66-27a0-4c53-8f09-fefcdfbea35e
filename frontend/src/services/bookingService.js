/**
 * Booking Service
 * API calls and booking-related operations
 */

import { calculatePriceBreakdown } from "../utils/pricingCalculator";

// API Configuration
const API_BASE_URL = "http://localhost:8000/api/v1";
const BOOKING_ENDPOINT = `${API_BASE_URL}/bookings`;

/**
 * Transform form data to API format
 * @param {Object} formData - Form data from the booking form
 * @returns {Object} Transformed data for API submission
 */
export const transformBookingData = (formData) => {
  const priceBreakdown = calculatePriceBreakdown(
    formData.cleaningType,
    formData.homeSize,
    formData.addOns
  );

  return {
    contactName: formData.name,
    contactEmail: formData.email,
    contactPhone: formData.phone,
    serviceType: formData.serviceType,
    packageType: formData.homeSize,
    address: formData.address,
    preferredDate: new Date().toISOString().split("T")[0],
    preferredTime: formData.preferredTime,
    notes: `Cleaning Type: ${formData.cleaningType}\nPrice: $${
      priceBreakdown.totalPrice
    }\nAdd-ons: ${formData.addOns.join(", ")}`,
    submittedAt: new Date().toISOString(),
  };
};

/**
 * Submit booking request to the API
 * @param {Object} formData - Form data from the booking form
 * @returns {Promise<Object>} API response data
 * @throws {Error} If the API request fails
 */
export const submitBooking = async (formData) => {
  try {
    const bookingData = transformBookingData(formData);

    const response = await fetch(BOOKING_ENDPOINT, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(bookingData),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(
        data.error?.message || "Failed to submit booking request"
      );
    }

    return data;
  } catch (error) {
    // Re-throw with more context if needed
    if (error.name === "TypeError" && error.message.includes("fetch")) {
      throw new Error(
        "Unable to connect to booking service. Please check your internet connection."
      );
    }
    throw error;
  }
};

/**
 * Simulate quote generation (for demo purposes)
 * @param {Object} formData - Form data from the quote form
 * @returns {Promise<Object>} Quote data
 */
export const generateQuote = async (formData) => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  const priceBreakdown = calculatePriceBreakdown(
    formData.cleaningType,
    formData.homeSize,
    formData.addOns
  );

  return {
    success: true,
    quote: priceBreakdown,
    formData,
  };
};

/**
 * Validate form data before submission
 * @param {Object} formData - Form data to validate
 * @returns {Object} Validation result with errors if any
 */
export const validateBookingForm = (formData) => {
  const errors = {};
  const requiredFields = [
    "name",
    "email",
    "phone",
    "address",
    "homeSize",
    "serviceType",
    "cleaningType",
    "preferredTime",
  ];

  // Check required fields
  requiredFields.forEach((field) => {
    if (!formData[field] || formData[field].trim() === "") {
      errors[field] = `${field} is required`;
    }
  });

  // Email validation
  if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.email = "Please enter a valid email address";
  }

  // Phone validation (basic)
  if (formData.phone && !/^[\d\s\-\\(\\)\\+]+$/.test(formData.phone)) {
    errors.phone = "Please enter a valid phone number";
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * Get booking status
 * @param {string} bookingId - ID of the booking
 * @returns {Promise<Object>} Booking status data
 */
export const getBookingStatus = async (bookingId) => {
  // eslint-disable-next-line no-useless-catch
  try {
    const response = await fetch(`${BOOKING_ENDPOINT}/${bookingId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error?.message || "Failed to get booking status");
    }

    return data;
  } catch (error) {
    throw error;
  }
};
