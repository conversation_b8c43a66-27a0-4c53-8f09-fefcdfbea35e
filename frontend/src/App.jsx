import React, { useState, useEffect } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { Toaster } from "react-hot-toast";
import Animation from "./components/Animation";
import Header from "./components/Header";
import Main from "./components/Main";
import Footer from "./components/Footer";
import Testimonials from "./components/Testimonials";
import About from "./components/About";
import Contact from "./components/Contact";
import FAQ from "./components/FAQ";
import StickyBookingBar from "./components/StickyBookingBar";
import AnimatedLogo from "./components/AnimatedLogo";
import Tips from "./components/Tips";
import HowItWorks from "./components/HowItWorks";
import OurWork from "./components/OurWork";
import BookingPopupRefactored from "./components/BookingPopupRefactored";
import ReviewPopup from "./components/ReviewPopup";
import useReviewPopup from "./hooks/useReviewPopup";
import ReviewPopupTest from "./components/ReviewPopupTest";

const App = () => {
  const [showLogo, setShowLogo] = useState(true);
  const [showBookingPopup, setShowBookingPopup] = useState(false);

  // Review popup management
  const {
    isReviewPopupOpen,
    bookingData,
    triggerReviewPopup,
    closeReviewPopup,
    handleReviewSubmit,
  } = useReviewPopup();

  useEffect(() => {
    // Hide logo after animation completes
    const timer = setTimeout(() => {
      setShowLogo(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const handleBookClick = () => {
    setShowBookingPopup(true);
  };

  return (
    <Router>
      <div className="min-h-screen flex flex-col">
        {showLogo && <AnimatedLogo />}
        <Header onBookClick={handleBookClick} />
        <main className="flex-grow">
          <Routes>
            <Route
              path="/"
              element={
                <>
                  <Main onBookClick={handleBookClick} />
                  <Testimonials />
                </>
              }
            />
            <Route path="/how-it-works" element={<HowItWorks />} />
            <Route
              path="/about"
              element={<About onBookClick={handleBookClick} />}
            />
            <Route path="/contact" element={<Contact />} />
            <Route path="/faq" element={<FAQ />} />
            <Route
              path="/tips"
              element={<Tips onBookClick={handleBookClick} />}
            />
            <Route path="/our-work" element={<OurWork />} />
          </Routes>
        </main>
        <Footer onBookClick={handleBookClick} />

        {/* Fixed Book Now Button */}
        <button
          onClick={() => setShowBookingPopup(true)}
          className="fixed bottom-4 right-4 sm:bottom-8 sm:right-8 bg-sky-500 hover:bg-sky-600 text-white px-6 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2 z-40 text-sm sm:text-base font-medium"
        >
          <span>Book Your Clean</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>

        {showBookingPopup ? (
          <BookingPopupRefactored
            isOpen={showBookingPopup}
            onClose={() => setShowBookingPopup(false)}
            onBookingSuccess={triggerReviewPopup}
          />
        ) : null}

        {/* Review Popup */}
        {isReviewPopupOpen ? (
          <ReviewPopup
            isOpen={isReviewPopupOpen}
            onClose={closeReviewPopup}
            bookingData={bookingData}
            onSubmit={handleReviewSubmit}
          />
        ) : null}

        {/* Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: "#10b981",
              color: "#fff",
              fontWeight: "500",
            },
            success: {
              iconTheme: {
                primary: "#10b981",
                secondary: "#fff",
              },
            },
          }}
        />
      </div>
    </Router>
  );
};

export default App;
