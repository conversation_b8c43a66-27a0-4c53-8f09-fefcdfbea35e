import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCalendarAlt,
  faStar,
  faHandPointer,
  faChevronRight,
  faHandshake,
  faClock,
  faClipboardCheck,
} from "@fortawesome/free-solid-svg-icons";

function HowItWorks() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-emerald-50 relative overflow-hidden">
      {/* Fun Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,_rgba(16,185,129,0.1)_0%,_transparent_50%)]"></div>
        <div className="absolute top-1/4 left-1/4 w-32 h-32 rounded-full bg-pink-200 blur-3xl"></div>
        <div className="absolute top-1/2 right-1/4 w-32 h-32 rounded-full bg-yellow-200 blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/2 w-32 h-32 rounded-full bg-blue-200 blur-3xl"></div>
      </div>

      {/* Hero Section */}
      <div className="relative h-[40vh] mt-18 bg-gradient-to-r from-emerald-500 to-emerald-600 flex items-center justify-center text-center text-white px-4">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 max-w-3xl mx-auto">
          <div className="inline-flex items-center gap-2 bg-emerald-100/20 text-emerald-100 px-4 py-2 rounded-full mb-6">
            <FontAwesomeIcon icon={faStar} className="text-yellow-400" />
            <span className="font-semibold">This is how we work</span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6 animate-fadeIn">
            Simple, Transparent, Free Quotes
          </h1>
          <p className="text-xl md:text-2xl animate-fadeIn delay-200">
            Select your service, book your appointment, and we come out for free
            to give you a quote—no obligation!
          </p>
        </div>
      </div>

      {/* Fun Bubble Steps Section */}
      <div className="max-w-6xl mx-auto px-4 py-20 relative">
        <div className="flex flex-col md:flex-row items-center justify-center gap-8 md:gap-0 relative">
          {/* Bubble 1 */}
          <div className="relative z-20 flex flex-col items-center md:w-1/3 mb-16 md:mb-0 animate-float">
            <div className="flex flex-col items-center justify-center p-3 transform hover:scale-105 transition-transform duration-300">
              <FontAwesomeIcon
                icon={faHandPointer}
                className="text-emerald-500 text-6xl mb-4"
              />
              <h3 className="text-2xl font-bold text-emerald-700 mb-2 text-center">
                Select Your Service
              </h3>
              <p className="text-emerald-600 text-base text-center px-4">
                Choose the cleaning or maintenance service you need.
              </p>
            </div>
            <div className="hidden md:block absolute right-[-20px] top-1/2 transform -translate-y-1/2 z-10">
              <FontAwesomeIcon
                icon={faChevronRight}
                className="text-emerald-300 text-4xl"
              />
            </div>
          </div>

          {/* Bubble 2 */}
          <div className="relative z-20 flex flex-col items-center md:w-1/3 mb-16 md:mb-0 animate-float delay-200">
            <div className=" flex flex-col items-center justify-center p-3 transform hover:scale-105 transition-transform duration-300">
              <FontAwesomeIcon
                icon={faCalendarAlt}
                className="text-pink-500 text-6xl mb-4"
              />
              <h3 className="text-2xl font-bold text-pink-700 mb-2 text-center">
                Book Your Appointment
              </h3>
              <p className="text-pink-600 text-base text-center px-4">
                Pick a date and time that works for you—easy and flexible
                scheduling.
              </p>
            </div>
            <div className="hidden md:block absolute right-[-30px] top-1/2 transform -translate-y-1/2 z-10">
              <FontAwesomeIcon
                icon={faChevronRight}
                className="text-pink-300 text-4xl"
              />
            </div>
          </div>

          {/* Bubble 3 */}
          <div className="relative z-20 flex flex-col items-center md:w-1/3 animate-float delay-400">
            <div className="flex flex-col items-center justify-center p-3 transform hover:scale-105 transition-transform duration-300">
              <FontAwesomeIcon
                icon={faClipboardCheck}
                className="text-yellow-500 text-6xl mb-4"
              />
              <h3 className="text-2xl font-bold text-yellow-700 mb-2 text-center">
                We Come Out for Free
              </h3>
              <p className="text-yellow-600 text-base text-center px-4">
                Our team visits your property and gives you a free,
                no-obligation quote on-site.
              </p>
            </div>
          </div>

          {/* Bubble connectors for mobile */}
          <div
            className="md:hidden flex flex-col items-center absolute left-1/2 top-40 z-0"
            style={{ height: "calc(100% - 160px)" }}
          >
            <div className="w-1 h-16 bg-emerald-200 rounded-full mt-4 mb-2"></div>
            <div className="w-1 h-16 bg-pink-200 rounded-ful mt-[220px] mb-2"></div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-6xl mx-auto mb-20 p-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">
            Why Choose Check Maid
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            We make professional cleaning simple, reliable, and effective
          </p>
        </div>
        <div className="grid md:grid-cols-2 place-items-center gap-8">
          <div className="max-w-[600px] bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center">
                <FontAwesomeIcon
                  icon={faClock}
                  className="text-emerald-600 text-xl"
                />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  Punctual Service
                </h3>
                <p className="text-gray-600">
                  We value your time. Our team arrives on schedule, every time,
                  ready to work.
                </p>
              </div>
            </div>
          </div>
          <div className="max-w-[600px] bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
                <FontAwesomeIcon
                  icon={faHandshake}
                  className="text-emerald-600 text-xl"
                />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  Trusted Professionals
                </h3>
                <p className="text-gray-600">
                  Our team is thoroughly vetted, trained, and insured for your
                  peace of mind.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r m-4  from-emerald-500 to-emerald-600 rounded-2xl p-8 text-white">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Get Started?</h2>
          <p className="text-xl mb-8">
            Experience the Check Maid difference today. Select your service and
            book your free, no-obligation quote.
          </p>
          <a
            href="/"
            className="inline-block bg-white text-emerald-600 px-8 py-4 rounded-full text-lg font-semibold
                       hover:bg-emerald-50 hover:scale-105 transition-all duration-300
                       shadow-lg hover:shadow-xl"
          >
            Get Started
          </a>
        </div>
      </div>
    </div>
  );
}

export default HowItWorks;
