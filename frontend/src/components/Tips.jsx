import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faStar, 
  faBroom, 
  faSprayCan, 
  faHandSparkles,
  faClock,
  faLeaf,
  faShieldAlt,
  faLightbulb,
  faCheckCircle,
} from '@fortawesome/free-solid-svg-icons';

function Tips({ onBookClick }) {
  const cleaningTips = [
    {
      category: "Kitchen Cleaning",
      tips: [
        {
          title: "Daily Counter Maintenance",
          description: "Wipe down countertops daily with a mild cleaner to prevent buildup. Use a microfiber cloth for best results.",
          icon: faBroom
        },
        {
          title: "Oven Cleaning",
          description: "Clean your oven regularly using a paste of baking soda and water. Let it sit overnight for tough stains.",
          icon: faSprayCan
        },
        {
          title: "Refrigerator Organization",
          description: "Keep your fridge clean by organizing items by category and wiping spills immediately.",
          icon: faHandSparkles
        }
      ]
    },
    {
      category: "Bathroom Maintenance",
      tips: [
        {
          title: "Shower Prevention",
          description: "Use a squeegee after each shower to prevent water spots and soap scum buildup.",
          icon: faBroom
        },
        {
          title: "Toilet Care",
          description: "Clean your toilet bowl weekly with a toilet brush and cleaner. Don't forget the base and behind the tank.",
          icon: faSprayCan
        },
        {
          title: "Grout Protection",
          description: "Seal grout lines annually to prevent staining and make cleaning easier.",
          icon: faShieldAlt
        }
      ]
    },
    {
      category: "Living Areas",
      tips: [
        {
          title: "Carpet Care",
          description: "Vacuum high-traffic areas daily and deep clean carpets every 6-12 months.",
          icon: faBroom
        },
        {
          title: "Dust Control",
          description: "Dust from top to bottom, using a microfiber cloth to trap particles instead of spreading them.",
          icon: faHandSparkles
        },
        {
          title: "Window Maintenance",
          description: "Clean windows on a cloudy day to prevent streaks. Use a squeegee for best results.",
          icon: faSprayCan
        }
      ]
    }
  ];

  const quickTips = [
    {
      icon: faClock,
      title: "Time-Saving Hacks",
      tips: [
        "Clean as you go to prevent buildup",
        "Use multi-purpose cleaners to reduce product clutter",
        "Keep cleaning supplies in each room for easy access"
      ]
    },
    {
      icon: faLeaf,
      title: "Eco-Friendly Solutions",
      tips: [
        "Use vinegar and baking soda for natural cleaning",
        "Opt for reusable cleaning cloths instead of paper towels",
        "Choose biodegradable cleaning products"
      ]
    },
    {
      icon: faCheckCircle,
      title: "Best Practices",
      tips: [
        "Always read product labels before use",
        "Test cleaners on small areas first",
        "Keep cleaning products out of reach of children"
      ]
    }
  ];

  const seasonalTips = [
    {
      season: "Spring Cleaning",
      tips: [
        "Deep clean carpets and rugs",
        "Wash windows inside and out",
        "Clean behind and under furniture",
        "Organize closets and storage areas"
      ]
    },
    {
      season: "Summer Maintenance",
      tips: [
        "Focus on outdoor living spaces",
        "Clean ceiling fans and air vents",
        "Maintain clean patios and decks",
        "Keep entryways free of dirt and debris"
      ]
    },
    {
      season: "Fall Preparation",
      tips: [
        "Clean gutters and downspouts",
        "Prepare heating systems for winter",
        "Deep clean before holiday season",
        "Organize storage for seasonal items"
      ]
    },
    {
      season: "Winter Care",
      tips: [
        "Maintain clean entryways to prevent salt stains",
        "Clean and organize holiday decorations",
        "Focus on indoor air quality",
        "Keep heating vents clean and clear"
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b mt-18 from-white to-emerald-50">
      {/* Hero Section */}
      <div className="relative h-[40vh] bg-gradient-to-r from-emerald-500 to-emerald-600">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative h-full flex items-center justify-center text-center text-white px-4">
          <div className="max-w-3xl">
            <div className="inline-flex items-center gap-2 bg-emerald-100/20 text-emerald-100 px-4 py-2 rounded-full mb-6">
              <FontAwesomeIcon icon={faStar} className="text-yellow-400" />
              <span className="font-semibold">Expert Cleaning Tips</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 animate-fadeIn">
              Professional Cleaning Tips & Tricks
            </h1>
            <p className="text-xl md:text-2xl animate-fadeIn delay-200">
              Learn the best practices for maintaining a clean and healthy home
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        {/* Room-Specific Tips */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">Room-by-Room Cleaning Guide</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {cleaningTips.map((section, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-emerald-600 mb-4">{section.category}</h3>
                <div className="space-y-4">
                  {section.tips.map((tip, tipIndex) => (
                    <div key={tipIndex} className="flex items-start gap-3">
                      <div className="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
                        <FontAwesomeIcon icon={tip.icon} className="text-emerald-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800">{tip.title}</h4>
                        <p className="text-gray-600 text-sm">{tip.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Tips Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">Quick Cleaning Tips</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {quickTips.map((section, index) => (
              <div key={index} className="bg-emerald-50 rounded-xl p-6">
                <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center mb-4">
                  <FontAwesomeIcon icon={section.icon} className="text-emerald-600 text-xl" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-4">{section.title}</h3>
                <ul className="space-y-2">
                  {section.tips.map((tip, tipIndex) => (
                    <li key={tipIndex} className="flex items-start gap-2">
                      <FontAwesomeIcon icon={faCheckCircle} className="text-emerald-500 mt-1" />
                      <span className="text-gray-600">{tip}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Seasonal Tips */}
        <div>
          <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">Seasonal Cleaning Guide</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {seasonalTips.map((season, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-emerald-600 mb-4">{season.season}</h3>
                <ul className="space-y-2">
                  {season.tips.map((tip, tipIndex) => (
                    <li key={tipIndex} className="flex items-start gap-2">
                      <FontAwesomeIcon icon={faLightbulb} className="text-amber-500 mt-1" />
                      <span className="text-gray-600">{tip}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 py-16">
        <div className="max-w-4xl mx-auto px-4 text-center text-white">
          <h2 className="text-3xl font-bold mb-6">Need Professional Cleaning Help?</h2>
          <p className="text-xl mb-8">Let our experts handle your cleaning needs while you focus on what matters most.</p>
          <button 
            onClick={onBookClick}
            className="bg-white text-emerald-600 px-8 py-4 rounded-full text-lg font-semibold
                     hover:bg-emerald-50 hover:scale-105 transition-all duration-300
                     shadow-lg hover:shadow-xl"
          >
            Schedule Your Cleaning
          </button>
        </div>
      </div>
    </div>
  );
}

export default Tips; 