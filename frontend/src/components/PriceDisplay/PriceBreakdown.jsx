/**
 * Price Breakdown Component
 * Displays detailed price breakdown for cleaning services
 */

import React from 'react';
import { calculatePriceBreakdown, getFormattedPrice } from '../../utils/pricingCalculator';
import { getCleaningDescription } from '../../utils/cleaningService';

const PriceBreakdown = ({ 
  cleaningType, 
  homeSize, 
  addOns = [],
  onBookingSubmit,
  onAdjustQuote,
  isSubmitting 
}) => {
  const priceBreakdown = calculatePriceBreakdown(cleaningType, homeSize, addOns);
  const formattedPrice = getFormattedPrice(cleaningType, homeSize, addOns);
  const cleaningDescription = getCleaningDescription(cleaningType);

  return (
    <div className="text-center py-4">
      {/* Discount Banner */}
      <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-3 mb-3">
        <p className="text-sm text-emerald-700 font-medium">
          🎉 You've unlocked 10% off with online booking!
        </p>
      </div>

      {/* Price Display */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
        {/* Main Price Section */}
        <div className="text-left">
          <h3 className="text-lg font-bold text-emerald-600 mb-2">
            Your Estimated Price
          </h3>
          <p className="text-3xl font-bold text-gray-900 mb-2">
            {formattedPrice}
          </p>
          <p className="text-sm text-gray-600">
            {cleaningDescription}
          </p>
        </div>

        {/* Price Breakdown Section */}
        <div className="bg-gray-50 rounded-lg p-3 text-left">
          <h4 className="font-medium text-gray-900 mb-2 text-sm">Price Breakdown:</h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Base Price:</span>
              <span className="text-gray-600">${priceBreakdown.basePrice}</span>
            </div>
            
            {addOns.length > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600">Add-ons ({addOns.length}):</span>
                <span className="text-gray-600">+${priceBreakdown.addOnsTotal}</span>
              </div>
            )}
            
            <div className="flex justify-between">
              <span className="text-gray-600">Subtotal:</span>
              <span className="text-gray-600">${priceBreakdown.subtotal}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-emerald-600">Online Discount (10%):</span>
              <span className="text-emerald-600">-${priceBreakdown.discount}</span>
            </div>
            
            <div className="flex justify-between pt-1 border-t border-gray-200">
              <span className="text-gray-900 font-medium">Total:</span>
              <span className="text-gray-900 font-medium">${priceBreakdown.totalPrice}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Disclaimer */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2 mb-4">
        <p className="text-sm text-yellow-800">
          ⓘ Prices are estimates and may vary after a detailed assessment.
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-2 justify-center">
        <button
          type="button"
          onClick={onBookingSubmit}
          disabled={isSubmitting}
          className="w-full sm:w-auto px-6 py-2.5 bg-sky-500 text-white text-sm font-medium rounded-lg hover:bg-sky-600 transition-all duration-300 focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 disabled:bg-sky-400 disabled:cursor-not-allowed flex items-center justify-center shadow-sm hover:shadow-md"
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </>
          ) : (
            "Let's Book It!"
          )}
        </button>
        
        <button
          type="button"
          onClick={onAdjustQuote}
          disabled={isSubmitting}
          className="w-full sm:w-auto px-6 py-2.5 text-gray-600 hover:text-gray-700 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          Adjust Quote
        </button>
      </div>
    </div>
  );
};

export default PriceBreakdown;
