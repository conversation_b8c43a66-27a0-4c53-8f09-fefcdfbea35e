import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPhone, faCalculator } from '@fortawesome/free-solid-svg-icons';

const StickyBookingBar = () => {
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-40">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between py-3">
          <div className="flex items-center gap-4">
            <a 
              href="tel:+13609691270" 
              className="flex items-center gap-2 text-emerald-600 hover:text-emerald-700 transition-colors"
            >
              <FontAwesomeIcon icon={faPhone} />
              <span>(*************</span>
            </a>
          </div>
          <button 
            onClick={() => {
              const calculatorSection = document.getElementById('calculator');
              if (calculatorSection) {
                calculatorSection.scrollIntoView({ behavior: 'smooth' });
              }
            }}
            className="flex items-center gap-2 px-4 py-2 bg-emerald-500 text-white rounded-lg
                     hover:bg-emerald-600 transition-colors duration-300"
          >
            <FontAwesomeIcon icon={faCalculator} />
            <span>Get a Quote</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default StickyBookingBar; 