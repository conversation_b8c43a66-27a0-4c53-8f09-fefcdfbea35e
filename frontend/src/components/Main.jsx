import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React from "react";
import {
  faClock,
  faShield,
  faLeaf,
  faCheck,
  faBuilding,
  faChevronRight,

} from "@fortawesome/free-solid-svg-icons";

function Main({ onBookClick }) {
  const ServiceCard = ({
    icon,
    title,
    description,
    onClick,
    isSelected,
    image,
  }) => (
    <div
      onClick={onClick}
      className={`bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300 cursor-pointer
                ${isSelected ? "ring-2 ring-emerald-500" : ""}`}
    >
      <div className="relative h-48 sm:h-56 group">
        <img
          src={image}
          alt={title}
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent"></div>
        <div className="absolute bottom-6 left-6 text-white">
          <div className="w-12 h-12 rounded-full bg-emerald-500/20 backdrop-blur-sm flex items-center justify-center mb-3">
            <FontAwesomeIcon icon={icon} className="text-emerald-300 text-xl" />
          </div>
          <h3 className="text-2xl font-bold mb-2">{title}</h3>
        </div>
      </div>
      <div className="p-6">
        <p className="text-gray-600 mb-6 leading-relaxed">{description}</p>

        <div className="space-y-4">
          <div className="flex items-center gap-3 text-gray-700">
            <FontAwesomeIcon icon={faCheck} className="text-emerald-500" />
            <span>Professional Equipment</span>
          </div>
          <div className="flex items-center gap-3 text-gray-700">
            <FontAwesomeIcon icon={faCheck} className="text-emerald-500" />
            <span>Licensed & Insured</span>
          </div>
          <div className="flex items-center gap-3 text-gray-700">
            <FontAwesomeIcon icon={faCheck} className="text-emerald-500" />
            <span>Customized Solutions</span>
          </div>
        </div>

        <div className="mt-6 flex items-center justify-between">
          <span className="text-emerald-600 font-semibold">Get a Quote</span>
          <FontAwesomeIcon icon={faChevronRight} className="text-emerald-600" />
        </div>
      </div>
    </div>
  );

  const PackageCard = ({
    icon,
    title,
    price,
    description,
    includes,
    perfectFor,
    image,
  }) => (
    <div className="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100">
      <div className="relative h-48 sm:h-56 group">
        <img
          src={image}
          alt={title}
          className={`w-full h-full ${
            image.includes("Applebee")
              ? "object-contain bg-white p-8"
              : "object-cover"
          } transition-transform duration-700 group-hover:scale-110`}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent transition-opacity duration-300 group-hover:opacity-90"></div>
        <div className="absolute bottom-6 left-6 text-white transform transition-transform duration-300 group-hover:translate-y-[-8px]">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 rounded-full bg-emerald-500/20 backdrop-blur-sm flex items-center justify-center">
              <FontAwesomeIcon
                icon={icon}
                className="text-emerald-300 text-xl"
              />
            </div>
            <span className="text-emerald-300 font-medium tracking-wide">
              BEST VALUE
            </span>
          </div>
          <h3 className="text-2xl sm:text-3xl font-bold mb-2">{title}</h3>
          <p className="text-2xl sm:text-3xl font-bold text-emerald-300">
            {price}
          </p>
        </div>
      </div>
      <div className="p-6 sm:p-8">
        <p className="text-lg text-gray-600 mb-8 leading-relaxed">
          {description}
        </p>

        <div className="mb-8">
          <h4 className="font-semibold text-gray-800 mb-4 text-base flex items-center gap-2">
            <div className="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center">
              <FontAwesomeIcon icon={faCheck} className="text-emerald-500" />
            </div>
            What's Included
          </h4>
          <ul className="space-y-3">
            {includes.map((item, index) => (
              <li
                key={index}
                className="flex items-start gap-3 text-gray-600 text-base hover:text-emerald-600 transition-colors duration-300"
              >
                <FontAwesomeIcon
                  icon={faCheck}
                  className="text-emerald-500 mt-1 flex-shrink-0"
                />
                <span className="leading-relaxed">{item}</span>
              </li>
            ))}
          </ul>
        </div>

        <div className="mb-8">
          <h4 className="font-semibold text-gray-800 mb-4 text-base flex items-center gap-2">
            <div className="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center">
              <FontAwesomeIcon icon={faBuilding} className="text-emerald-500" />
            </div>
            Perfect For
          </h4>
          <ul className="space-y-3">
            {perfectFor.map((item, index) => (
              <li
                key={index}
                className="flex items-start gap-3 text-gray-600 text-base hover:text-emerald-600 transition-colors duration-300"
              >
                <FontAwesomeIcon
                  icon={faCheck}
                  className="text-emerald-500 mt-1 flex-shrink-0"
                />
                <span className="leading-relaxed">{item}</span>
              </li>
            ))}
          </ul>
        </div>

        <button
          onClick={onBookClick}
          className="w-full py-4 px-6 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white font-semibold text-lg rounded-xl hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg hover:shadow-emerald-500/20 border border-emerald-400/20"
        >
          Select Package
        </button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Video Background */}
        <div className="absolute inset-0 w-full h-full">
          <video
            autoPlay
            loop
            muted
            playsInline
            className="w-full h-full object-cover"
          >
            <source src="/backgroundVideo.mp4" type="video/mp4" />
          </video>
          {/* Dark Overlay */}
          <div className="absolute inset-0 bg-black/50"></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-10 text-center text-white px-4 max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Professional Cleaning Services
          </h1>
          <p className="text-xl md:text-2xl mb-8">
            You do the relaxing. We do the cleaning.
          </p>
          <button
            onClick={onBookClick}
            className="bg-emerald-600 text-white px-8 py-3 rounded-lg text-lg font-medium hover:bg-emerald-700 transition-colors"
          >
            Get Instant Quote (2 min)
          </button>
        </div>
      </section>

      {/* Features Section */}
      <div className="py-8 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3">
              <div className="w-10 h-10 bg-sky-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <FontAwesomeIcon
                  icon={faShield}
                  className="text-lg text-sky-600"
                />
              </div>
              <h3 className="text-sm font-semibold mb-1">Trained Staff</h3>
              <p className="text-xs text-gray-600">
                Professional & background-checked
              </p>
            </div>
            <div className="text-center p-3">
              <div className="w-10 h-10 bg-sky-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <FontAwesomeIcon
                  icon={faLeaf}
                  className="text-lg text-sky-600"
                />
              </div>
              <h3 className="text-sm font-semibold mb-1">Eco-Friendly</h3>
              <p className="text-xs text-gray-600">Green cleaning products</p>
            </div>
            <div className="text-center p-3">
              <div className="w-10 h-10 bg-sky-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <FontAwesomeIcon
                  icon={faClock}
                  className="text-lg text-sky-600"
                />
              </div>
              <h3 className="text-sm font-semibold mb-1">Flexible Hours</h3>
              <p className="text-xs text-gray-600">Book anytime online</p>
            </div>
            <div className="text-center p-3">
              <div className="w-10 h-10 bg-sky-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <FontAwesomeIcon
                  icon={faShield}
                  className="text-lg text-sky-600"
                />
              </div>
              <h3 className="text-sm font-semibold mb-1">Insured & Bonded</h3>
              <p className="text-xs text-gray-600">Fully protected service</p>
            </div>
          </div>

          {/* Services Section */}
          {/* <div className="py-8 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-800 mb-4">
                  Select Your Service
                </h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Choose the type of cleaning service that best fits your needs
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <ServiceCard
                  icon={faHouse}
                  title="Residential Cleaning"
                  description="Regular maintenance cleaning for your home"
                  onClick={() => handleServiceClick("residential")}
                  isSelected={selectedService === "residential"}
                  image="/residential.jpg"
                />
                <ServiceCard
                  icon={faBuilding}
                  title="Commercial Cleaning"
                  description="Professional cleaning for businesses"
                  onClick={() => handleServiceClick("commercial")}
                  isSelected={selectedService === "commercial"}
                  image="/commercial.jpg"
                />
                <ServiceCard
                  icon={faBuilding}
                  title="Airbnb Cleaning"
                  description="Fast cleans between guest stays"
                  onClick={() => handleServiceClick("airbnb")}
                  isSelected={selectedService === "airbnb"}
                  image="/airbnb.jpg"
                />
              </div>
            </div>
          </div> */}

          {/* Commercial Services Showcase - Applebee's */}
          <section className="w-full mt-12">
            <div className="max-w-7xl mx-auto px-0 sm:px-0 flex flex-col items-center">
              <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2 text-center">
                Trusted by Applebee's
              </h3>
              <p className="text-lg md:text-xl text-gray-700 mb-6 text-center max-w-2xl">
                We provide exterior maintenance for Applebee's, including
                pressure washing, lawn care, hedge trimming, and more.
              </p>
            </div>
            <div
              className="relative w-full flex justify-center items-center overflow-hidden"
              style={{ minHeight: "220px" }}
            >
              <div className="w-full max-w-4xl mx-auto">
                <img
                  src="/worker.jpeg"
                  alt="Applebee's Exterior Maintenance - Worker in Action"
                  className="w-full h-[220px] md:h-[340px] object-cover object-center rounded-xl shadow-none"
                  style={{ maxHeight: "340px" }}
                />
              </div>
            </div>
          </section>

          {/* Areas We Service Section (modern pill badges with description) */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6">
            <div className="border-t border-sky-100 mb-2"></div>
            <div className="text-center mb-4">
              <span className="text-base font-semibold text-sky-700 tracking-wide">
                Locations We Service
              </span>
            </div>
            <div className="flex flex-wrap justify-center gap-3">
              {[
                "Oak Harbor",
                "Anacortes",
                "Burlington",
                "La Conner",
                "Coupeville",
              ].map((city) => (
                <div
                  key={city}
                  className="flex items-center justify-center h-12 px-6 bg-white border border-sky-200 shadow-sm rounded-md text-sky-800 text-sm font-semibold tracking-wide"
                >
                  {city}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section - seamless integration */}
      <div className="bg-white pt-8 pb-20">
        <div className="max-w-4xl mx-auto text-center px-6">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Experience the Difference?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Join thousands of satisfied customers who trust us with their
            cleaning needs
          </p>
          <button
            onClick={onBookClick}
            className="bg-emerald-600 hover:bg-emerald-700 text-white font-semibold py-4 px-8 rounded-lg text-lg transition-colors duration-300"
          >
            Get Instant Quote (2 min)
          </button>
        </div>
      </div>
    </div>
  );
}

export default Main;
