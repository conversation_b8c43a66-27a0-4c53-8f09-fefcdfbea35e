import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faStar } from '@fortawesome/free-solid-svg-icons';
import ContactInfo from './contact/ContactInfo';
import ContactForm from './contact/ContactForm';

function Contact() {

  return (
    <div className="min-h-screen bg-gradient-to-b mt-10 from-white to-emerald-50">
      {/* Hero Section */}
      <div className="relative h-[40vh] bg-gradient-to-r from-emerald-500 to-emerald-600">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative h-full flex items-center justify-center text-center text-white px-4">
          <div className="max-w-3xl">
            <div className="inline-flex items-center gap-2 bg-emerald-100/20 text-emerald-100 px-4 py-2 rounded-full mb-6">
              <FontAwesomeIcon icon={faStar} className="text-yellow-400" />
              <span className="font-semibold">We're Here to Help</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 animate-fadeIn">
              Contact Us
            </h1>
            <p className="text-xl md:text-2xl animate-fadeIn delay-200">
              Your satisfaction is our priority
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        <div className="grid md:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <ContactInfo />
          </div>

          {/* Contact Form */}
          <ContactForm />
        </div>
      </div>
    </div>
  );
}

export default Contact;