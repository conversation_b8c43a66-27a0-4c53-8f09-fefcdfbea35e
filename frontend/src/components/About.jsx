import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faStar, 
  faShieldAlt, 
  faLeaf, 
  faAward,
  faHandshake,
  faChartLine,
  faHeart
} from '@fortawesome/free-solid-svg-icons';

function About({ onBookClick }) {
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-emerald-50">
      {/* Hero Section */}
      <div className="relative h-[50vh] bg-gradient-to-r from-emerald-500 to-emerald-600">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative h-full flex items-center justify-center text-center text-white px-4">
          <div className="max-w-3xl">
            <div className="inline-flex items-center gap-2 bg-emerald-100/20 text-emerald-100 px-4 py-2 rounded-full mb-6">
              <FontAwesomeIcon icon={faStar} className="text-yellow-400" />
              <span className="font-semibold">Our Story</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 animate-fadeIn">
              Transforming Spaces, <br />
              <span className="text-emerald-100">Enhancing Lives</span>
            </h1>
            <p className="text-xl md:text-2xl animate-fadeIn delay-200">
              Professional cleaning services with a personal touch
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        {/* Story Section */}
        <div className="mb-20">
          <div className=" gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-800 mb-6">Our Journey</h2>
              <p className="text-gray-600 mb-4">
                Founded in 2017 in Oak Harbor, CheckMaid has become the region's most trusted cleaning service. Our clients love us—they stay with us for years, and many say they wouldn't trust anyone else with their home or business.
              </p>
              <p className="text-gray-600 mb-4">
                At the heart of our company is Claudia Sommerville, our manager, whose dedication and care have shaped the CheckMaid experience. Many of our long-time clients see Claudia as the face of our brand, and her commitment to quality and service is a big reason why so many people recommend us to their friends and family.
              </p>
              <div className="flex items-center gap-4 mt-6">
                <div className="flex items-center gap-2">
                  <FontAwesomeIcon icon={faAward} className="text-emerald-500" />
                  <span className="text-gray-700">15+ Years Experience</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Values Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Our Core Values</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              These principles guide everything we do, from our cleaning methods to our customer service
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
              <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center mb-4">
                <FontAwesomeIcon icon={faShieldAlt} className="text-emerald-600 text-xl" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">Reliability</h3>
              <p className="text-gray-600">
                We show up on time, every time, with the same high standards you expect.
              </p>
            </div>
            <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
              <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center mb-4">
                <FontAwesomeIcon icon={faLeaf} className="text-emerald-600 text-xl" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">Eco-Friendly</h3>
              <p className="text-gray-600">
                We use environmentally safe products and sustainable cleaning practices.
              </p>
            </div>
            <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
              <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center mb-4">
                <FontAwesomeIcon icon={faHeart} className="text-emerald-600 text-xl" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">Care</h3>
              <p className="text-gray-600">
                We treat your home with the same respect and attention as our own.
              </p>
            </div>
          </div>
        </div>

        {/* Team Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Meet Our Leadership Team</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Our experienced team is dedicated to providing the best service possible
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="aspect-w-1 aspect-h-1">
                <img 
                  src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=688&q=80" 
                  alt="Claudia Sommerville" 
                  className="object-cover w-full h-full"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-800 mb-1">Claudia Sommerville</h3>
                <p className="text-emerald-600 font-medium mb-2">Operations Manager</p>
                <p className="text-gray-600">
                  With 15 years of experience in the cleaning industry, Claudia ensures our services meet the highest standards.
                </p>
              </div>
            </div>
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="aspect-w-1 aspect-h-1">
                <img 
                  src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=761&q=80" 
                  alt="Jennifer Andrade" 
                  className="object-cover w-full h-full"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-800 mb-1">Jennifer Andrade</h3>
                <p className="text-emerald-600 font-medium mb-2">Transportation Specialist</p>
                <p className="text-gray-600">
                  Jennifer expertly manages our transportation logistics, ensuring our team arrives on time and fully equipped for every job.
                </p>
              </div>
            </div>
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="aspect-w-1 aspect-h-1">
                <img 
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80" 
                  alt="Berta" 
                  className="object-cover w-full h-full"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-800 mb-1">Berto</h3>
                <p className="text-emerald-600 font-medium mb-2">Manager</p>
                <p className="text-gray-600">
                  Known for his exceptional work ethic and attention to detail, Berto sets the standard for excellence in our cleaning services.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Guarantee Section */}
        <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-2xl p-8 text-white">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">Our Guarantee</h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center flex-shrink-0">
                  <FontAwesomeIcon icon={faHandshake} className="text-xl" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">100% Satisfaction</h3>
                  <p className="text-emerald-100">
                    If you're not completely satisfied with our service, we'll make it right.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center flex-shrink-0">
                  <FontAwesomeIcon icon={faChartLine} className="text-xl" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">Consistent Quality</h3>
                  <p className="text-emerald-100">
                    We maintain the same high standards for every cleaning service.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 py-16">
        <div className="max-w-4xl mx-auto px-4 text-center text-white">
          <div className="inline-flex items-center gap-2 bg-emerald-100/20 text-emerald-100 px-4 py-2 rounded-full mb-6">
            <FontAwesomeIcon icon={faStar} className="text-yellow-400" />
            <span className="font-semibold">Ready to Get Started?</span>
          </div>
          <h2 className="text-3xl font-bold mb-6">Experience the Check Maid Difference</h2>
          <p className="text-xl mb-8">Join our growing family of satisfied customers today.</p>
          <button 
            onClick={onBookClick}
            className="bg-white text-emerald-600 px-8 py-4 rounded-full text-lg font-semibold
                     hover:bg-emerald-50 hover:scale-105 transition-all duration-300
                     shadow-lg hover:shadow-xl"
          >
            Book Your First Clean
          </button>
        </div>
      </div>
    </div>
  );
}

export default About; 