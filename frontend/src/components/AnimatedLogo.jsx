import React, { useState, useEffect } from 'react';
import logo from '../assets/checkmaid-logo.png';

function AnimatedLogo() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Trigger animation after component mounts
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-white z-50">
      <div 
        className={`transform transition-all duration-1000 ease-out ${
          isVisible 
            ? 'translate-y-0 opacity-100 scale-100' 
            : '-translate-y-20 opacity-0 scale-90'
        }`}
      >
        <img
          src={logo}
          alt="Check Maid Logo"
          className="h-24 w-auto object-contain"
        />
      </div>
    </div>
  );
}

export default AnimatedLogo; 