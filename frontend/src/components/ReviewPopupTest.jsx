import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlay } from '@fortawesome/free-solid-svg-icons';
import useReviewPopup from '../hooks/useReviewPopup';
import ReviewPopup from './ReviewPopup';

const ReviewPopupTest = () => {
  const {
    isReviewPopupOpen,
    bookingData,
    triggerReviewPopup,
    closeReviewPopup,
    handleReviewSubmit
  } = useReviewPopup();

  const testBookingData = {
    bookingNumber: 'TEST-123',
    contactName: '<PERSON>',
    contactEmail: '<EMAIL>',
    contactPhone: '(*************',
    serviceType: 'residential',
    address: {
      city: 'Burlington',
      state: 'WA'
    }
  };

  const handleTestTrigger = () => {
    console.log('🧪 Test: Triggering review popup manually');
    triggerReviewPopup(testBookingData);
  };

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <button
        onClick={handleTestTrigger}
        className="bg-purple-500 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-purple-600 transition-colors flex items-center gap-2"
      >
        <FontAwesomeIcon icon={faPlay} />
        Test Review Popup
      </button>

      <ReviewPopup
        isOpen={isReviewPopupOpen}
        onClose={closeReviewPopup}
        bookingData={bookingData}
        onSubmit={handleReviewSubmit}
      />
    </div>
  );
};

export default ReviewPopupTest;
