import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faStar, faCamera, faCheckCircle } from '@fortawesome/free-solid-svg-icons';

function OurWork() {
  const galleryItems = [
    {
      title: "Kitchen Transformation",
      description: "Before & After Deep Cleaning",
      beforeImage: "https://images.unsplash.com/photo-1556911220-bff31c812dba?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80",
      afterImage: "https://images.unsplash.com/photo-1556911220-bff31c812dba?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80",
      features: ["Deep cleaning", "Grout restoration", "Appliance cleaning"]
    },
    {
      title: "Bathroom Makeover",
      description: "Before & After Deep Cleaning",
      beforeImage: "https://images.unsplash.com/photo-1584622781857-0a0c0c0c0c0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80",
      afterImage: "https://images.unsplash.com/photo-1584622781857-0a0c0c0c0c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80",
      features: ["Tile cleaning", "Grout restoration", "Sanitization"]
    },
    {
      title: "Living Room Refresh",
      description: "Before & After Deep Cleaning",
      beforeImage: "https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80",
      afterImage: "https://images.unsplash.com/photo-1616486338812-3dadae4b4ace?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80",
      features: ["Carpet cleaning", "Dust removal", "Furniture cleaning"]
    },
    {
      title: "Office Space",
      description: "Before & After Commercial Cleaning",
      beforeImage: "https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80",
      afterImage: "https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80",
      features: ["Floor maintenance", "Window cleaning", "Sanitization"]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-emerald-50">
      {/* Hero Section */}
      <div className="relative h-[40vh] bg-gradient-to-r from-emerald-500 to-emerald-600">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative h-full flex items-center justify-center text-center text-white px-4">
          <div className="max-w-3xl">
            <div className="inline-flex items-center gap-2 bg-emerald-100/20 text-emerald-100 px-4 py-2 rounded-full mb-6">
              <FontAwesomeIcon icon={faCamera} className="text-yellow-400" />
              <span className="font-semibold">Our Work</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 animate-fadeIn">
              See the Difference We Make
            </h1>
            <p className="text-xl md:text-2xl animate-fadeIn delay-200">
              Real results from our satisfied customers
            </p>
          </div>
        </div>
      </div>

      {/* Gallery Section */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-800 px-4 py-2 rounded-full mb-4">
            <FontAwesomeIcon icon={faStar} className="text-yellow-400" />
            <span className="font-semibold">Before & After</span>
          </div>
          <h2 className="text-3xl font-bold text-emerald-600">Our Work Speaks for Itself</h2>
        </div>
        <div className="grid md:grid-cols-2 gap-8">
          {galleryItems.map((item, index) => (
            <div key={index} className="relative group">
              <div className="relative h-96 rounded-xl overflow-hidden shadow-xl border-2 border-emerald-100">
                <img 
                  src={item.beforeImage} 
                  alt={`${item.title} Before`} 
                  className="absolute inset-0 w-full h-full object-cover transition-opacity duration-500 group-hover:opacity-0"
                />
                <img 
                  src={item.afterImage} 
                  alt={`${item.title} After`} 
                  className="absolute inset-0 w-full h-full object-cover opacity-0 transition-opacity duration-500 group-hover:opacity-100"
                />
                <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-emerald-900/80 to-transparent text-white">
                  <h3 className="text-xl font-semibold">{item.title}</h3>
                  <p className="text-sm mb-2">{item.description}</p>
                  <div className="flex flex-wrap gap-2">
                    {item.features.map((feature, i) => (
                      <span key={i} className="inline-flex items-center gap-1 text-xs bg-emerald-600/50 px-2 py-1 rounded-full">
                        <FontAwesomeIcon icon={faCheckCircle} className="text-xs" />
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 py-16">
        <div className="max-w-4xl mx-auto px-4 text-center text-white">
          <div className="inline-flex items-center gap-2 bg-emerald-100/20 text-emerald-100 px-4 py-2 rounded-full mb-6">
            <FontAwesomeIcon icon={faStar} className="text-yellow-400" />
            <span className="font-semibold">Ready to Transform Your Space?</span>
          </div>
          <h2 className="text-3xl font-bold mb-6">Experience the Check Maid Difference</h2>
          <p className="text-xl mb-8">Let us bring the same level of care and attention to your home or office.</p>
          <button 
            onClick={() => document.getElementById('calculator')?.scrollIntoView({ behavior: 'smooth' })}
            className="bg-white text-emerald-600 px-8 py-4 rounded-full text-lg font-semibold
                     hover:bg-emerald-50 hover:scale-105 transition-all duration-300
                     shadow-lg hover:shadow-xl"
          >
            Get Your Free Quote
          </button>
        </div>
      </div>
    </div>
  );
}

export default OurWork; 