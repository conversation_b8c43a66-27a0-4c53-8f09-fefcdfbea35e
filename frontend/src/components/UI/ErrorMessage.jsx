/**
 * Error Message Component
 * Reusable error message display component
 */

import React from 'react';

const ErrorMessage = ({
  error,
  className = '',
  variant = 'default'
}) => {
  if (!error) return null;

  const variantClasses = {
    default: 'bg-red-50 border-red-200 text-red-600',
    inline: 'text-red-600',
    banner: 'bg-red-100 border-red-300 text-red-700',
  };

  const baseClasses = variant === 'inline'
    ? 'text-sm mt-1'
    : 'px-4 py-3 rounded-lg text-sm border';

  // Handle different error formats
  const renderError = () => {
    if (typeof error === 'string') {
      return error;
    }

    if (typeof error === 'object' && error.title) {
      // Handle structured error object
      return (
        <div className="space-y-2">
          <p className="font-medium">{error.title}</p>
          {error.details && <p>Error details: {error.details}</p>}
          {error.fallback && <p>{error.fallback}</p>}
          {error.note && <p className="text-sm text-gray-600">{error.note}</p>}
        </div>
      );
    }

    return error;
  };

  return (
    <div className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
      {renderError()}
    </div>
  );
};

export default ErrorMessage;
