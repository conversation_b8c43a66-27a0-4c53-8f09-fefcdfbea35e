import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMapMarkerAlt } from '@fortawesome/free-solid-svg-icons';

const ServiceAreaMap = () => {
  return (
    <div className="relative w-full max-w-md mx-auto">
      {/* Map Container */}
      <div className="relative w-full aspect-[4/3] bg-gradient-to-br from-emerald-50 to-white rounded-2xl overflow-hidden shadow-lg">
        {/* Map Background - Using a simple gradient for now, can be replaced with actual map image */}
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-100 to-emerald-50">
          {/* Island Shape */}
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-3/4 h-3/4 bg-white rounded-full opacity-20"></div>
        </div>

        {/* Location Markers */}
        <div className="absolute top-1/4 left-1/4">
          <div className="relative">
            <div className="absolute -inset-4 bg-emerald-400 rounded-full opacity-20 animate-ping"></div>
            <FontAwesomeIcon 
              icon={faMapMarkerAlt} 
              className="text-emerald-600 text-2xl relative z-10"
            />
            <div className="absolute -bottom-6 left-1/2 -translate-x-1/2 whitespace-nowrap text-sm font-medium text-emerald-800">
              Oak Harbor
            </div>
          </div>
        </div>

        <div className="absolute top-1/3 right-1/4">
          <div className="relative">
            <div className="absolute -inset-4 bg-emerald-400 rounded-full opacity-20 animate-ping" style={{ animationDelay: '0.5s' }}></div>
            <FontAwesomeIcon 
              icon={faMapMarkerAlt} 
              className="text-emerald-600 text-2xl relative z-10"
            />
            <div className="absolute -bottom-6 left-1/2 -translate-x-1/2 whitespace-nowrap text-sm font-medium text-emerald-800">
              Coupeville
            </div>
          </div>
        </div>

        <div className="absolute bottom-1/4 left-1/3">
          <div className="relative">
            <div className="absolute -inset-4 bg-emerald-400 rounded-full opacity-20 animate-ping" style={{ animationDelay: '1s' }}></div>
            <FontAwesomeIcon 
              icon={faMapMarkerAlt} 
              className="text-emerald-600 text-2xl relative z-10"
            />
            <div className="absolute -bottom-6 left-1/2 -translate-x-1/2 whitespace-nowrap text-sm font-medium text-emerald-800">
              Anacortes
            </div>
          </div>
        </div>

        {/* Service Area Circle */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-full">
          <div className="absolute inset-0 border-4 border-emerald-400 rounded-full opacity-30"></div>
          <div className="absolute inset-4 border-4 border-emerald-400 rounded-full opacity-20"></div>
          <div className="absolute inset-8 border-4 border-emerald-400 rounded-full opacity-10"></div>
        </div>

        {/* Legend */}
        <div className="absolute bottom-4 left-4 bg-white/90 p-2 rounded-lg shadow-sm">
          <div className="flex items-center gap-2 text-sm text-emerald-800">
            <div className="w-3 h-3 bg-emerald-400 rounded-full"></div>
            <span>Service Area</span>
          </div>
        </div>
      </div>

      {/* Description */}
      <div className="mt-4 text-center">
        <p className="text-sm text-gray-600">
          Serving Whidbey & Fidalgo Island communities
        </p>
      </div>
    </div>
  );
};

export default ServiceAreaMap; 