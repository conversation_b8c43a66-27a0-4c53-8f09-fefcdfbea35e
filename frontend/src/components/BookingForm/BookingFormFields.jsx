/**
 * Booking Form Fields Component
 * Reusable form fields for booking form
 */

import React from 'react';
import {
  getCleaningTypeOptions,
  getHomeSizeOptions,
  getServiceTypeOptions,
  getPreferredTimeOptions,
  getCleaningDescription,
} from '../../utils/cleaningService';

const BookingFormFields = ({
  formData,
  onInputChange,
  onFieldBlur,
  getFieldError,
  hasFieldError,
  isSubmitting,
}) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
      {/* Full Name */}
      <div>
        <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
          Full Name *
        </label>
        <input
          type="text"
          name="name"
          value={formData.name}
          onChange={onInputChange}
          onBlur={onFieldBlur}
          required
          disabled={isSubmitting}
          className={`w-full px-4 py-2.5 text-base border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed ${
            hasFieldError('name') ? 'border-red-300' : 'border-gray-200'
          }`}
          placeholder="John Doe"
        />
        {getFieldError('name') && (
          <p className="mt-1 text-sm text-red-600">{getFieldError('name')}</p>
        )}
      </div>

      {/* Email Address */}
      <div>
        <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
          Email Address *
        </label>
        <input
          type="email"
          name="email"
          value={formData.email}
          onChange={onInputChange}
          onBlur={onFieldBlur}
          required
          disabled={isSubmitting}
          className={`w-full px-4 py-2.5 text-base border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed ${
            hasFieldError('email') ? 'border-red-300' : 'border-gray-200'
          }`}
          placeholder="<EMAIL>"
        />
        {getFieldError('email') && (
          <p className="mt-1 text-sm text-red-600">{getFieldError('email')}</p>
        )}
      </div>

      {/* Phone Number */}
      <div>
        <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
          Phone Number *
        </label>
        <input
          type="tel"
          name="phone"
          value={formData.phone}
          onChange={onInputChange}
          onBlur={onFieldBlur}
          required
          disabled={isSubmitting}
          className={`w-full px-4 py-2.5 text-base border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed ${
            hasFieldError('phone') ? 'border-red-300' : 'border-gray-200'
          }`}
          placeholder="(*************"
        />
        {getFieldError('phone') && (
          <p className="mt-1 text-sm text-red-600">{getFieldError('phone')}</p>
        )}
      </div>

      {/* Property Address */}
      <div>
        <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
          Property Address *
        </label>
        <input
          type="text"
          name="address"
          value={formData.address}
          onChange={onInputChange}
          onBlur={onFieldBlur}
          required
          disabled={isSubmitting}
          className={`w-full px-4 py-2.5 text-base border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed ${
            hasFieldError('address') ? 'border-red-300' : 'border-gray-200'
          }`}
          placeholder="123 Main St, City, State ZIP"
        />
        {getFieldError('address') && (
          <p className="mt-1 text-sm text-red-600">{getFieldError('address')}</p>
        )}
      </div>

      {/* Home Size */}
      <div>
        <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
          Home Size *
        </label>
        <select
          name="homeSize"
          value={formData.homeSize}
          onChange={onInputChange}
          onBlur={onFieldBlur}
          required
          disabled={isSubmitting}
          className={`w-full px-4 py-2.5 text-base border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed ${
            hasFieldError('homeSize') ? 'border-red-300' : 'border-gray-200'
          }`}
        >
          {getHomeSizeOptions().map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {getFieldError('homeSize') && (
          <p className="mt-1 text-sm text-red-600">{getFieldError('homeSize')}</p>
        )}
      </div>

      {/* Service Type */}
      <div>
        <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
          Service Type *
        </label>
        <select
          name="serviceType"
          value={formData.serviceType}
          onChange={onInputChange}
          onBlur={onFieldBlur}
          required
          disabled={isSubmitting}
          className={`w-full px-4 py-2.5 text-base border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed ${
            hasFieldError('serviceType') ? 'border-red-300' : 'border-gray-200'
          }`}
        >
          {getServiceTypeOptions().map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {getFieldError('serviceType') && (
          <p className="mt-1 text-sm text-red-600">{getFieldError('serviceType')}</p>
        )}
      </div>

      {/* Cleaning Type */}
      <div>
        <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
          Cleaning Type *
        </label>
        <select
          name="cleaningType"
          value={formData.cleaningType}
          onChange={onInputChange}
          onBlur={onFieldBlur}
          required
          disabled={isSubmitting}
          className={`w-full px-4 py-2.5 text-base border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed ${
            hasFieldError('cleaningType') ? 'border-red-300' : 'border-gray-200'
          }`}
        >
          {getCleaningTypeOptions().map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {getFieldError('cleaningType') && (
          <p className="mt-1 text-sm text-red-600">{getFieldError('cleaningType')}</p>
        )}
      </div>

      {/* Preferred Time */}
      <div className="sm:col-span-1">
        <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
          Preferred Time *
        </label>
        <select
          name="preferredTime"
          value={formData.preferredTime}
          onChange={onInputChange}
          onBlur={onFieldBlur}
          required
          disabled={isSubmitting}
          className={`w-full px-4 py-2.5 text-base border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed ${
            hasFieldError('preferredTime') ? 'border-red-300' : 'border-gray-200'
          }`}
        >
          {getPreferredTimeOptions().map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {getFieldError('preferredTime') && (
          <p className="mt-1 text-sm text-red-600">{getFieldError('preferredTime')}</p>
        )}
      </div>
    </div>
  );
};

export default BookingFormFields;
