/**
 * Refactored Booking Popup Component
 * Main orchestrator component using separation of concerns
 */

import React from "react";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

// Custom Hooks
import { useBookingForm } from "../hooks/useBookingForm";
import { useBookingSubmission } from "../hooks/useBookingSubmission";

// Components
import BookingFormFields from "./BookingForm/BookingFormFields";
import PriceBreakdown from "./PriceDisplay/PriceBreakdown";
import LoadingSpinner from "./UI/LoadingSpinner";
import ErrorMessage from "./UI/ErrorMessage";

// Utils
import { getCleaningDescription } from "../utils/cleaningService";

const BookingPopupRefactored = ({ isOpen, onClose, onBookingSuccess }) => {
  // Form state management
  const {
    formData,
    handleInputChange,
    handleFieldBlur,
    getFieldError,
    hasFieldError,
    isFormReady,
    validateForm,
  } = useBookingForm();

  // Submission state management
  const {
    isSubmitting,
    error,
    showPrice,
    handleQuoteSubmit,
    handleBookingSubmit,
    goBackToForm,
    clearError,
  } = useBookingSubmission(onBookingSuccess);

  // Handle quote form submission
  const onQuoteSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    await handleQuoteSubmit(formData);
  };

  // Handle booking submission
  const onBookingSubmit = async () => {
    const success = await handleBookingSubmit(formData, () => {
      // Close the booking popup first
      onClose();
    });

    // If booking was successful, the triggerReviewPopup will be called
    // by the useBookingSubmission hook
    console.log('Booking submission completed, success:', success);
  };

  // Handle input changes and clear errors
  const onInputChange = (e) => {
    handleInputChange(e);
    if (error) clearError();
  };

  // Don't render if not open
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-100 px-4 sm:px-8 py-4 z-10">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl sm:text-2xl font-semibold text-gray-900">
                Get Your Instant Quote
              </h2>
              <p className="text-sm sm:text-base text-gray-500">
                Takes just 2 minutes to get your price
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors p-2 -m-2"
              disabled={isSubmitting}
            >
              <FontAwesomeIcon icon={faTimes} className="text-xl" />
            </button>
          </div>
        </div>

        {/* Form Content */}
        <form onSubmit={onQuoteSubmit} className="p-4 sm:p-8 space-y-4">
          {/* Error Display */}
          <ErrorMessage error={error} />

          {showPrice ? (
            /* Price Display */
            <PriceBreakdown
              cleaningType={formData.cleaningType}
              homeSize={formData.homeSize}
              addOns={formData.addOns}
              onBookingSubmit={onBookingSubmit}
              onAdjustQuote={goBackToForm}
              isSubmitting={isSubmitting}
            />
          ) : (
            /* Form Fields */
            <>
              <BookingFormFields
                formData={formData}
                onInputChange={onInputChange}
                onFieldBlur={handleFieldBlur}
                getFieldError={getFieldError}
                hasFieldError={hasFieldError}
                isSubmitting={isSubmitting}
              />

              {/* Cleaning Description */}
              {formData.cleaningType && (
                <p className="text-sm sm:text-base text-gray-600 mt-3">
                  {getCleaningDescription(formData.cleaningType)}
                </p>
              )}

              {/* Submit Button */}
              <div className="pt-4">
                <button
                  type="submit"
                  disabled={isSubmitting || !isFormReady()}
                  className="w-full bg-sky-500 text-white py-3 px-6 rounded-lg text-base font-medium hover:bg-sky-600 transition-all duration-300 focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 disabled:bg-sky-400 disabled:cursor-not-allowed flex items-center justify-center shadow-sm hover:shadow-md"
                >
                  {isSubmitting ? (
                    <>
                      <LoadingSpinner
                        size="md"
                        color="white"
                        className="-ml-1 mr-3"
                      />
                      Processing...
                    </>
                  ) : (
                    "Get Instant Quote"
                  )}
                </button>
              </div>
            </>
          )}
        </form>
      </div>


    </div>
  );
};

export default BookingPopupRefactored;
