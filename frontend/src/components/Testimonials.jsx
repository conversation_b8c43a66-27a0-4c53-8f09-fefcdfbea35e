import React, { useState, useEffect, useCallback } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faStar, faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import testimonialService from '../services/testimonialService';

// Fallback testimonials in case API fails
const fallbackTestimonials = [
  {
    name: "<PERSON>",
    rating: 5,
    text: "<PERSON> <PERSON> did an amazing job before our move-out. Highly recommend! The team was professional, thorough, and left our place spotless.",
    location: "Burlington, WA",
    image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"
  },
  {
    name: "<PERSON>",
    rating: 5,
    text: "I've been using <PERSON> <PERSON>'s services for my office for over a year now. Consistent quality and great customer service. They're always on time and do a fantastic job.",
    location: "Mount Vernon, WA",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"
  },
  {
    name: "Emily Rodriguez",
    rating: 5,
    text: "The deep cleaning service was exactly what we needed. They paid attention to every detail and used eco-friendly products. Will definitely book again!",
    location: "Anacortes, WA",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"
  },
  {
    name: "David Wilson",
    rating: 5,
    text: "Professional, efficient, and thorough. Check Maid transformed our new home before move-in. The team was friendly and left everything sparkling clean.",
    location: "Oak Harbor, WA",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"
  }
];

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isHeadingVisible, setIsHeadingVisible] = useState(false);
  const [testimonials, setTestimonials] = useState(fallbackTestimonials);
  const [loading, setLoading] = useState(true);


  // Fetch testimonials from API
  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        setLoading(true);
        const response = await testimonialService.getFeaturedTestimonials(10);
        if (response.success && response.data.length > 0) {
          setTestimonials(response.data);
        }
      } catch (err) {
        console.error('Failed to fetch testimonials:', err);
        // Keep fallback testimonials on error
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  useEffect(() => {
    // Trigger heading animation after component mount
    const timer = setTimeout(() => {
      setIsHeadingVisible(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  const nextTestimonial = useCallback(() => {
    if (!isAnimating) {
      setIsAnimating(true);
      setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
      setTimeout(() => setIsAnimating(false), 500);
    }
  }, [isAnimating, testimonials.length]);

  const prevTestimonial = () => {
    if (!isAnimating) {
      setIsAnimating(true);
      setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);
      setTimeout(() => setIsAnimating(false), 500);
    }
  };

  // Auto-rotate testimonials
  useEffect(() => {
    if (!loading && testimonials.length > 1) {
      const interval = setInterval(nextTestimonial, 5000);
      return () => clearInterval(interval);
    }
  }, [loading, testimonials.length, nextTestimonial]);

  const currentTestimonial = testimonials[currentIndex];

  // Show loading state
  if (loading) {
    return (
      <div className="w-full py-16 bg-gradient-to-b from-emerald-50 to-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-300 rounded w-64 mx-auto mb-4"></div>
              <div className="h-6 bg-gray-300 rounded w-96 mx-auto mb-8"></div>
              <div className="bg-white p-8 rounded-xl shadow-lg">
                <div className="w-24 h-24 bg-gray-300 rounded-full mx-auto mb-6"></div>
                <div className="h-4 bg-gray-300 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-300 rounded w-3/4 mx-auto mb-4"></div>
                <div className="h-6 bg-gray-300 rounded w-32 mx-auto"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full py-16 bg-gradient-to-b from-emerald-50 to-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-800 px-4 py-2 rounded-full mb-4">
            <FontAwesomeIcon icon={faStar} className="text-yellow-400" />
            <span className="font-semibold">Customer Reviews</span>
          </div>
          <h2
            className={`text-4xl font-bold text-gray-900 mb-4
              transform transition-all duration-1000 ease-out
              ${isHeadingVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
          >
            What Our Customers Say
          </h2>
          <p
            className={`text-xl text-gray-600
              transform transition-all duration-1000 ease-out delay-200
              ${isHeadingVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
          >
            Don't just take our word for it - hear from our satisfied customers
          </p>
        </div>

        <div className="relative">
          {/* Navigation Arrows */}
          <button
            onClick={prevTestimonial}
            className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 sm:-translate-x-8 z-10
                     bg-white p-3 rounded-full shadow-lg hover:bg-emerald-50 transition-all duration-300
                     focus:outline-none focus:ring-2 focus:ring-emerald-500"
            aria-label="Previous testimonial"
          >
            <FontAwesomeIcon icon={faChevronLeft} className="text-emerald-600" />
          </button>

          <button
            onClick={nextTestimonial}
            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 sm:translate-x-8 z-10
                     bg-white p-3 rounded-full shadow-lg hover:bg-emerald-50 transition-all duration-300
                     focus:outline-none focus:ring-2 focus:ring-emerald-500"
            aria-label="Next testimonial"
          >
            <FontAwesomeIcon icon={faChevronRight} className="text-emerald-600" />
          </button>

          {/* Single Testimonial Card */}
          <div className="bg-white p-8 rounded-xl shadow-lg transition-all duration-500 transform hover:shadow-xl">
            <div className="flex flex-col items-center text-center">
              <div className="w-24 h-24 rounded-full overflow-hidden mb-6 border-4 border-emerald-100">
                <img
                  src={currentTestimonial.image}
                  alt={currentTestimonial.name}
                  className="w-full h-full object-cover"
                />
              </div>

              <div className="flex mb-4">
                {[...Array(currentTestimonial.rating)].map((_, i) => (
                  <FontAwesomeIcon
                    key={i}
                    icon={faStar}
                    className="text-yellow-400 text-lg"
                  />
                ))}
              </div>

              <p className="text-gray-600 mb-6 italic text-lg leading-relaxed max-w-2xl">
                "{currentTestimonial.text}"
              </p>

              <div className="mt-4">
                <p className="font-semibold text-gray-800 text-lg">{currentTestimonial.name}</p>
                <p className="text-sm text-gray-500">{currentTestimonial.location}</p>
              </div>
            </div>
          </div>

          {/* Dots Navigation */}
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300
                  ${index === currentIndex ? 'bg-emerald-500 scale-125' : 'bg-gray-300'}`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Testimonials;