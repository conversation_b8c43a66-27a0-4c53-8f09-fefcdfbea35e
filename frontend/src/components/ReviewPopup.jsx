import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faStar, 
  faTimes, 
  faHeart,
  faThumbsUp
} from '@fortawesome/free-solid-svg-icons';

// Random placeholder images for customers
const placeholderImages = [
  'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150&q=80',
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150&q=80',
  'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150&q=80',
  'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150&q=80',
  'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150&q=80',
  'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150&q=80',
  'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150&q=80',
  'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150&q=80',
  'https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150&q=80',
  'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150&q=80'
];

// Get random placeholder image
const getRandomPlaceholderImage = () => {
  return placeholderImages[Math.floor(Math.random() * placeholderImages.length)];
};

const ReviewPopup = ({ 
  isOpen, 
  onClose, 
  bookingData = {}, 
  onSubmit 
}) => {
  const [formData, setFormData] = useState({
    name: bookingData.contactName || '',
    rating: 5,
    text: '',
    location: bookingData.address?.city ? `${bookingData.address.city}, ${bookingData.address.state || 'WA'}` : '',
    serviceType: bookingData.serviceType || 'general'
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showThankYou, setShowThankYou] = useState(false);
  const [hoveredRating, setHoveredRating] = useState(0);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.text.trim()) {
      alert('Please write a review before submitting.');
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare testimonial data with random placeholder image
      const testimonialData = {
        ...formData,
        image: getRandomPlaceholderImage(),
        customerEmail: bookingData.contactEmail || '',
        customerPhone: bookingData.contactPhone || '',
        source: 'website',
        isActive: true,
        isApproved: true,
        isFeatured: true,
      };

      // Call the onSubmit callback
      if (onSubmit) {
        await onSubmit(testimonialData);
      }

      // Show thank you message
      setShowThankYou(true);
      
      // Auto close after 3 seconds
      setTimeout(() => {
        onClose();
        setShowThankYou(false);
      }, 3000);

    } catch (error) {
      console.error('Error submitting review:', error);
      alert('There was an error submitting your review. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle rating click
  const handleRatingClick = (rating) => {
    setFormData(prev => ({ ...prev, rating }));
  };

  // Handle input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-screen overflow-y-auto">
        {showThankYou ? (
          // Thank You Screen
          <div className="p-8 text-center">
            <div className="mb-6">
              <div className="w-20 h-20 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FontAwesomeIcon icon={faHeart} className="text-3xl text-emerald-500" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Thank You!</h2>
              <p className="text-gray-600">
                Your review has been submitted and will be published after approval.
              </p>
            </div>
            
            <div className="flex items-center justify-center space-x-2 text-emerald-500 mb-4">
              <FontAwesomeIcon icon={faThumbsUp} />
              <span className="text-sm font-medium">We appreciate your feedback!</span>
              <FontAwesomeIcon icon={faThumbsUp} />
            </div>
            
            <div className="text-xs text-gray-500">
              This window will close automatically...
            </div>
          </div>
        ) : (
          // Review Form
          <>
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h2 className="text-xl font-bold text-gray-900">Share Your Experience</h2>
                <p className="text-sm text-gray-600">Help others by leaving a review</p>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <FontAwesomeIcon icon={faTimes} className="text-xl" />
              </button>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Rating */}
              <div className="text-center">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  How was your experience?
                </label>
                <div className="flex justify-center space-x-2 mb-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      type="button"
                      onClick={() => handleRatingClick(star)}
                      onMouseEnter={() => setHoveredRating(star)}
                      onMouseLeave={() => setHoveredRating(0)}
                      className="text-3xl transition-all duration-200 hover:scale-110"
                    >
                      <FontAwesomeIcon
                        icon={faStar}
                        className={`${
                          star <= (hoveredRating || formData.rating)
                            ? 'text-yellow-400'
                            : 'text-gray-300'
                        }`}
                      />
                    </button>
                  ))}
                </div>
                <p className="text-xs text-gray-500">
                  {formData.rating === 5 && "Excellent! ⭐"}
                  {formData.rating === 4 && "Very Good! 👍"}
                  {formData.rating === 3 && "Good 👌"}
                  {formData.rating === 2 && "Fair 😐"}
                  {formData.rating === 1 && "Poor 😞"}
                </p>
              </div>

              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Your Name
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                  placeholder="Enter your name"
                />
              </div>

              {/* Location */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Location
                </label>
                <input
                  type="text"
                  required
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                  placeholder="City, State"
                />
              </div>

              {/* Review Text */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Your Review
                </label>
                <textarea
                  required
                  rows={4}
                  value={formData.text}
                  onChange={(e) => handleInputChange('text', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors resize-none"
                  placeholder="Tell others about your experience with our service..."
                  maxLength={500}
                />
                <div className="text-right text-xs text-gray-500 mt-1">
                  {formData.text.length}/500 characters
                </div>
              </div>

              {/* Service Type (Hidden, auto-filled) */}
              <input type="hidden" value={formData.serviceType} />

              {/* Submit Button */}
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={onClose}
                  className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                >
                  Maybe Later
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting || !formData.text.trim()}
                  className="flex-1 px-6 py-3 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium flex items-center justify-center"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <FontAwesomeIcon icon={faThumbsUp} className="mr-2" />
                      Submit Review
                    </>
                  )}
                </button>
              </div>

              {/* Privacy Note */}
              <div className="text-xs text-gray-500 text-center bg-gray-50 p-3 rounded-lg">
                <FontAwesomeIcon icon={faThumbsUp} className="mr-1" />
                Your review will be reviewed before being published publicly.
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  );
};

export default ReviewPopup;
