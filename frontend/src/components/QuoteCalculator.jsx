import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faStar, faChevronLeft, faChevronRight, faHouse, faBroom, faBuilding, faBoxOpen, faHammer, faKey, faCalendar, faCheck, faUser, faEnvelope, faPhone, faMapMarkerAlt, faClipboard, faHome, faDollarSign } from '@fortawesome/free-solid-svg-icons';

const services = [
  {
    id: 'standard',
    name: 'Standard / General Cleaning',
    icon: faHouse,
    description: 'Regular maintenance cleaning for your home',
    pricePerSqFt: 0.15
  },
  {
    id: 'deep',
    name: 'Deep Cleaning',
    icon: faBroom,
    description: 'Thorough cleaning for a fresh start',
    pricePerSqFt: 0.25
  },
  {
    id: 'commercial',
    name: 'Commercial Cleaning',
    icon: faBuilding,
    description: 'Professional cleaning for businesses',
    pricePerSqFt: 0.20
  },
  {
    id: 'move',
    name: 'Move In/Out Cleaning',
    icon: faBoxOpen,
    description: 'Complete reset for new tenants or owners',
    pricePerSqFt: 0.30
  },
  {
    id: 'construction',
    name: 'Post-Construction Cleaning',
    icon: faHammer,
    description: 'Clean-up after renovations or new builds',
    pricePerSqFt: 0.35
  },
  {
    id: 'vacation',
    name: 'Vacation Rental Turnover',
    icon: faKey,
    description: 'Fast cleans between guest stays',
    pricePerSqFt: 0.25
  }
];

const frequencies = [
  { id: 'weekly', name: 'Weekly', discount: 0.1 },
  { id: 'bi-weekly', name: 'Bi-Weekly', discount: 0.05 },
  { id: 'one-time', name: 'One-Time', discount: 0 }
];

function QuoteCalculator() {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    service: '',
    size: 1000,
    frequency: 'one-time',
    name: '',
    email: '',
    phone: '',
    address: '',
    instructions: ''
  });
  const [estimatedPrice, setEstimatedPrice] = useState(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  const calculatePrice = () => {
    const selectedService = services.find(s => s.id === formData.service);
    if (!selectedService || !formData.size) return null;

    const basePrice = selectedService.pricePerSqFt * formData.size;
    const frequencyDiscount = frequencies.find(f => f.id === formData.frequency)?.discount || 0;
    const discountedPrice = basePrice * (1 - frequencyDiscount);
    
    // Apply 20% discount in steps 4 and 5
    if (currentStep === 4 || currentStep === 5) {
      return (discountedPrice * 0.8).toFixed(2);
    }
    
    return discountedPrice.toFixed(2);
  };

  const handleServiceSelect = (serviceId) => {
    setFormData(prev => ({
      ...prev,
      service: serviceId
    }));
    setCurrentStep(2);
  };

  const handleSizeChange = (e) => {
    const value = parseInt(e.target.value);
    setFormData(prev => ({
      ...prev,
      size: value
    }));
  };

  const handleFrequencySelect = (frequencyId) => {
    setFormData(prev => ({
      ...prev,
      frequency: frequencyId
    }));
    const price = calculatePrice();
    setEstimatedPrice(price);
    setCurrentStep(4);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would typically send the form data to your backend
    console.log('Booking submitted:', {
      ...formData,
      finalPrice: calculatePrice(),
      originalPrice: estimatedPrice
    });
    // Reset form or show success message
    setCurrentStep(1);
    setFormData({
      service: '',
      size: 1000,
      frequency: 'one-time',
      name: '',
      email: '',
      phone: '',
      address: '',
      instructions: ''
    });
  };

  const getSelectedService = () => {
    return services.find(s => s.id === formData.service);
  };

  const getSelectedFrequency = () => {
    return frequencies.find(f => f.id === formData.frequency);
  };

  const SelectionTracker = () => {
    if (!formData.service) return null; // Don't show tracker until service is selected
    
    return (
      <div className="bg-white rounded-xl shadow-lg p-6 mb-8 border-2 border-emerald-100">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Your Selections</h3>
        <div className="space-y-4">
          {/* Service Selection */}
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
              <FontAwesomeIcon icon={faBroom} className="text-emerald-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Service</p>
              <p className="font-medium text-gray-800">
                {getSelectedService()?.name}
              </p>
            </div>
          </div>

          {/* Size Selection */}
          {formData.size > 0 && (
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
                <FontAwesomeIcon icon={faHome} className="text-emerald-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Size</p>
                <p className="font-medium text-gray-800">
                  {formData.size} sq ft
                </p>
              </div>
            </div>
          )}

          {/* Frequency Selection */}
          {formData.frequency && (
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
                <FontAwesomeIcon icon={faCalendar} className="text-emerald-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Frequency</p>
                <p className="font-medium text-gray-800">
                  {getSelectedFrequency()?.name}
                </p>
              </div>
            </div>
          )}

          {/* Price Display */}
          {estimatedPrice && (
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
                <FontAwesomeIcon icon={faDollarSign} className="text-emerald-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Estimated Price</p>
                <p className="font-medium text-emerald-600">${estimatedPrice}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-emerald-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-800 px-4 py-2 rounded-full mb-4">
            <FontAwesomeIcon icon={faStar} className="text-yellow-400" />
            <span className="font-semibold">Get Your Instant Quote</span>
          </div>
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {currentStep === 1 && "Select Your Service"}
            {currentStep === 2 && "Enter Your Space Size"}
            {currentStep === 3 && "Choose Frequency"}
            {currentStep === 4 && "Your Quote"}
            {currentStep === 5 && "Your Details"}
            {currentStep === 6 && "Payment"}
          </h2>
          <p className="text-xl text-gray-600">
            {currentStep === 1 && "Choose the cleaning service that best fits your needs"}
            {currentStep === 2 && "Enter the square footage of the space to be cleaned"}
            {currentStep === 3 && "How often would you like the service?"}
            {currentStep === 4 && "Here's your estimated price"}
            {currentStep === 5 && "Complete your booking details"}
            {currentStep === 6 && "Complete your payment to confirm booking"}
          </p>
        </div>

        {/* Progress Bar */}
        <div className="flex justify-center mb-12">
          <div className="flex items-center space-x-4">
            {[1, 2, 3, 4, 5, 6].map((stepNumber) => (
              <React.Fragment key={stepNumber}>
                <div className={`flex items-center justify-center w-10 h-10 rounded-full 
                  ${currentStep >= stepNumber ? 'bg-emerald-500 text-white' : 'bg-gray-200 text-gray-500'}`}>
                  {stepNumber}
                </div>
                {stepNumber < 6 && (
                  <div className={`w-16 h-1 ${currentStep > stepNumber ? 'bg-emerald-500' : 'bg-gray-200'}`} />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {/* Selection Tracker - Left Column */}
          <div className="md:col-span-1">
            <SelectionTracker />
          </div>

          {/* Main Content - Right Column */}
          <div className="md:col-span-2">
        {/* Step 1: Service Selection */}
            {currentStep === 1 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {services.map((service) => (
              <button
                key={service.id}
                    onClick={() => handleServiceSelect(service.id)}
                className={`p-6 rounded-xl border-2 transition-all duration-300
                      ${formData.service === service.id 
                    ? 'border-emerald-500 bg-emerald-50' 
                    : 'border-gray-200 hover:border-emerald-300'}`}
              >
                <div className="flex flex-col items-center text-center">
                  <div className="w-16 h-16 rounded-full bg-emerald-100 flex items-center justify-center mb-4">
                        <FontAwesomeIcon icon={service.icon} className="text-2xl text-emerald-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{service.name}</h3>
                  <p className="text-gray-600">{service.description}</p>
                </div>
              </button>
            ))}
          </div>
        )}

            {/* Step 2: Size Selection */}
            {currentStep === 2 && (
              <div className="bg-white rounded-xl shadow-lg p-8 border-2 border-emerald-100">
                <div className="text-center mb-8">
                  <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-800 px-4 py-2 rounded-full mb-4">
                    <FontAwesomeIcon icon={faHome} className="text-emerald-600" />
                    <span className="font-semibold">Step 2: Size</span>
                  </div>
                  <h2 className="text-3xl font-bold text-emerald-600">What's the size of your space?</h2>
                  <p className="text-gray-600 mt-2">Select the approximate square footage of the area to be cleaned</p>
                </div>
                
                <div className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <label htmlFor="size" className="block text-gray-700 font-medium">
                        Square Footage: {formData.size} sq ft
                </label>
                      <span className="text-emerald-600 font-semibold">
                        {formData.size < 1000 ? 'Small' : 
                         formData.size < 2000 ? 'Medium' : 
                         formData.size < 3000 ? 'Large' : 'Extra Large'}
                      </span>
                    </div>
                    
                    <div className="relative">
                <input
                        type="range"
                        id="size"
                        name="size"
                        min="500"
                        max="5000"
                        step="100"
                        value={formData.size}
                        onChange={handleSizeChange}
                        className="w-full h-2 bg-emerald-100 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="absolute -top-6 left-0 right-0 flex justify-between text-xs text-gray-500">
                        <span>500 sq ft</span>
                        <span>5000 sq ft</span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-4 gap-2 text-center text-sm text-gray-600">
                      <div className={`p-2 rounded-lg ${formData.size < 1000 ? 'bg-emerald-100 text-emerald-700' : 'bg-gray-100'}`}>
                        Small<br/>(500-999)
                      </div>
                      <div className={`p-2 rounded-lg ${formData.size >= 1000 && formData.size < 2000 ? 'bg-emerald-100 text-emerald-700' : 'bg-gray-100'}`}>
                        Medium<br/>(1000-1999)
                      </div>
                      <div className={`p-2 rounded-lg ${formData.size >= 2000 && formData.size < 3000 ? 'bg-emerald-100 text-emerald-700' : 'bg-gray-100'}`}>
                        Large<br/>(2000-2999)
                      </div>
                      <div className={`p-2 rounded-lg ${formData.size >= 3000 ? 'bg-emerald-100 text-emerald-700' : 'bg-gray-100'}`}>
                        Extra Large<br/>(3000+)
                      </div>
                    </div>
              </div>
                  
                  <div className="flex justify-between pt-6">
                    <button
                      onClick={() => setCurrentStep(1)}
                      className="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-all duration-300"
                    >
                      Back
                    </button>
                    <button
                      onClick={() => setCurrentStep(3)}
                      className="bg-emerald-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-emerald-700 transition-all duration-300"
                    >
                      Next
                    </button>
              </div>
            </div>
          </div>
        )}

            {/* Step 3: Frequency Selection */}
            {currentStep === 3 && (
          <div className="max-w-md mx-auto">
            <div className="bg-white p-8 rounded-xl shadow-lg">
              <div className="space-y-4">
                    {frequencies.map((option) => (
                  <button
                        key={option.id}
                        onClick={() => handleFrequencySelect(option.id)}
                    className={`w-full p-4 rounded-lg border-2 transition-all duration-300
                          ${formData.frequency === option.id 
                        ? 'border-emerald-500 bg-emerald-50' 
                        : 'border-gray-200 hover:border-emerald-300'}`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-900 capitalize">
                            {option.name}
                      </span>
                          {formData.frequency === option.id && (
                        <span className="text-emerald-500">✓</span>
                      )}
                    </div>
                  </button>
                ))}
              </div>
                  <div className="flex justify-between pt-6">
                    <button
                      onClick={() => setCurrentStep(2)}
                      className="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-all duration-300"
                    >
                      Back
                    </button>
              </div>
            </div>
          </div>
        )}

            {/* Step 4: Quote Display */}
            {currentStep === 4 && (
          <div className="max-w-md mx-auto">
            <div className="bg-white p-8 rounded-xl shadow-lg">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Your Estimated Price</h3>
                    <div className="space-y-4">
                      <div className="text-4xl font-bold text-emerald-600">
                        ${calculatePrice()}
                      </div>
                      <div className="text-gray-600 line-through">
                        Original Price: ${estimatedPrice}
                      </div>
                      <div className="text-emerald-600 font-semibold">
                        20% Discount Applied!
                </div>
                </div>
                    <button 
                      onClick={() => setCurrentStep(5)}
                      className="mt-8 w-full py-4 px-6 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white font-semibold rounded-xl 
                           hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 transform hover:-translate-y-1 
                           hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 shadow-lg">
                  Book Now
                </button>
              </div>
            </div>
          </div>
        )}

            {/* Step 5: Booking Form */}
            {currentStep === 5 && (
              <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8 border-2 border-emerald-100">
                <div className="text-center mb-8">
                  <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-800 px-4 py-2 rounded-full mb-4">
                    <FontAwesomeIcon icon={faUser} className="text-emerald-600" />
                    <span className="font-semibold">Step 5: Your Details</span>
                  </div>
                  <h2 className="text-3xl font-bold text-emerald-600">Complete Your Booking</h2>
                  <p className="text-gray-600 mt-2">Please provide your contact information to finalize your booking</p>
                  
                  {/* Price Display with Discount */}
                  <div className="mt-6 bg-emerald-50 p-4 rounded-lg">
                    <div className="flex items-center justify-center gap-4">
                      <div className="text-gray-600 line-through">${estimatedPrice}</div>
                      <div className="text-2xl font-bold text-emerald-600">${calculatePrice()}</div>
                    </div>
                    <div className="text-emerald-600 font-semibold mt-2">
                      20% Discount Applied!
                    </div>
                  </div>
                </div>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="name">
                      Full Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="email">
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="phone">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="address">
                      Service Address
                    </label>
                    <input
                      type="text"
                      id="address"
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="instructions">
                      Special Instructions
                    </label>
                    <textarea
                      id="instructions"
                      name="instructions"
                      value={formData.instructions}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
                      rows="4"
                    />
                  </div>

                  <div className="flex justify-between pt-6">
                    <button
                      type="button"
                      onClick={() => setCurrentStep(4)}
                      className="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-all duration-300"
                    >
                      Back
                    </button>
                    <button
                      type="submit"
                      onClick={() => setCurrentStep(6)}
                      className="bg-emerald-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-emerald-700 transition-all duration-300"
                    >
                      Proceed to Payment
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* Step 6: Payment */}
            {currentStep === 6 && (
              <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8 border-2 border-emerald-100">
                <div className="text-center mb-8">
                  <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-800 px-4 py-2 rounded-full mb-4">
                    <FontAwesomeIcon icon={faDollarSign} className="text-emerald-600" />
                    <span className="font-semibold">Step 6: Payment</span>
                  </div>
                  <h2 className="text-3xl font-bold text-emerald-600">Complete Your Payment</h2>
                  <p className="text-gray-600 mt-2">Secure payment to confirm your booking</p>
                  
                  {/* Simple Price Display */}
                  <div className="mt-6 bg-emerald-50 p-4 rounded-lg">
                    <div className="text-3xl font-bold text-emerald-600">
                      ${calculatePrice()}
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      (20% discount included)
                    </div>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Quick Payment Options */}
                  <div className="space-y-4">
                    <div className="text-center text-gray-600">Choose your payment method</div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <button
                        className="flex items-center justify-center gap-2 p-4 border-2 border-gray-200 rounded-lg hover:border-emerald-300 transition-all duration-300"
                      >
                        <span className="text-2xl">💳</span>
                        <span className="font-medium">Pay with Card</span>
                      </button>
                      <button
                        className="flex items-center justify-center gap-2 p-4 border-2 border-gray-200 rounded-lg hover:border-emerald-300 transition-all duration-300"
                      >
                        <span className="text-2xl">🍎</span>
                        <span className="font-medium">Pay with Apple Pay</span>
                      </button>
                      <button
                        className="flex items-center justify-center gap-2 p-4 border-2 border-gray-200 rounded-lg hover:border-emerald-300 transition-all duration-300"
                      >
                        <span className="text-2xl">💙</span>
                        <span className="font-medium">Pay with PayPal</span>
                      </button>
                      <button
                        className="flex items-center justify-center gap-2 p-4 border-2 border-gray-200 rounded-lg hover:border-emerald-300 transition-all duration-300"
                      >
                        <span className="text-2xl">💵</span>
                        <span className="font-medium">Pay with Google Pay</span>
                      </button>
                    </div>
                  </div>

                  {/* Card Payment Form - Only show when card is selected */}
                  <div className="space-y-4">
                    <div>
                      <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="cardNumber">
                        Card Number
                      </label>
                      <input
                        type="text"
                        id="cardNumber"
                        name="cardNumber"
                        placeholder="1234 5678 9012 3456"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
                        required
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="expiry">
                          Expiry Date
                        </label>
                        <input
                          type="text"
                          id="expiry"
                          name="expiry"
                          placeholder="MM/YY"
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="cvv">
                          CVV
                        </label>
                        <input
                          type="text"
                          id="cvv"
                          name="cvv"
                          placeholder="123"
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between pt-6">
            <button
                      type="button"
                      onClick={() => setCurrentStep(5)}
                      className="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-all duration-300"
            >
              Back
            </button>
                    <button
                      type="submit"
                      onClick={handleSubmit}
                      className="bg-emerald-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-emerald-700 transition-all duration-300"
                    >
                      Pay ${calculatePrice()}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default QuoteCalculator;
