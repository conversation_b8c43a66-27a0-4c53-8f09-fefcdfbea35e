import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown, faChevronUp, faStar } from '@fortawesome/free-solid-svg-icons';

function FAQ() {
  const [openIndex, setOpenIndex] = useState(null);

  const faqs = [
    {
      question: "How do I get an instant quote?",
      answer: "Just click the 'Get Instant Quote (2 min)' button on our homepage! Fill out a short form and you'll see a ballpark price right away—no waiting, no hassle."
    },
    {
      question: "Are you insured and bonded?",
      answer: "Absolutely! We are fully insured and bonded for your peace of mind. All our staff are background-checked and professionally trained."
    },
    {
      question: "What types of cleaning do you offer?",
      answer: "We offer residential cleaning (light, medium, deep), move-in/move-out, and commercial services. We also provide exterior maintenance like pressure washing, lawn care, and hedge trimming for businesses such as Applebee's."
    },
    {
      question: "Are your products safe and eco-friendly?",
      answer: "Yes! We use only eco-friendly, non-toxic cleaning products that are safe for your family, pets, and the environment."
    },
    {
      question: "Can I book online and get a discount?",
      answer: "Yes! Book online and you'll receive a 10% discount on your first service. Our booking process is fast, easy, and secure."
    },
    {
      question: "Do I need to be home during the cleaning?",
      answer: "No, you don't need to be home during the cleaning. Many of our clients provide us with keys or access codes. We are fully insured and all our cleaners undergo thorough background checks."
    },
    {
      question: "What's included in a standard cleaning?",
      answer: "Our standard cleaning includes dusting, vacuuming, mopping, bathroom cleaning, kitchen cleaning, and general tidying. We can customize the service based on your specific needs."
    },
    {
      question: "How do I schedule a cleaning?",
      answer: "You can schedule a cleaning through our website, by phone, or by email. We offer flexible scheduling options including one-time, weekly, bi-weekly, or monthly services."
    },
    {
      question: "What's your cancellation policy?",
      answer: "We require 24 hours notice for cancellations. Cancellations made within 24 hours may be subject to a fee. We understand emergencies happen and will work with you in those situations."
    },
    {
      question: "Are your cleaners insured and background checked?",
      answer: "Yes, all our cleaners are fully insured and have undergone thorough background checks. We also provide ongoing training to ensure the highest quality of service."
    }
  ];

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b mt-24 from-white to-emerald-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header Section */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-800 px-3 py-1.5 rounded-full mb-2">
            <FontAwesomeIcon icon={faStar} className="text-yellow-400" />
            <span className="font-semibold text-sm">Frequently Asked Questions</span>
          </div>
          <h1 className="text-2xl md:text-3xl font-bold text-emerald-600 mb-2">How Can We Help?</h1>
          <p className="text-base text-gray-600">Find answers to common questions about our cleaning services</p>
        </div>

        {/* FAQ List */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="bg-white rounded-xl shadow-lg overflow-hidden border-2 border-emerald-100 hover:border-emerald-200 transition-all duration-300"
            >
              <button
                className="w-full px-6 py-4 text-left focus:outline-none"
                onClick={() => toggleFAQ(index)}
              >
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-emerald-700">{faq.question}</h3>
                  <FontAwesomeIcon
                    icon={openIndex === index ? faChevronUp : faChevronDown}
                    className="text-emerald-500"
                  />
                </div>
              </button>
              
              <div
                className={`px-6 pb-4 transition-all duration-300 ease-in-out ${
                  openIndex === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                }`}
              >
                <p className="text-gray-600">{faq.answer}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Help Section */}
        <div className="mt-12 text-center">
          <p className="text-gray-600 mb-4">Still have questions?</p>
          <a
            href="/contact"
            className="inline-block bg-gradient-to-r from-emerald-500 to-emerald-600 text-white py-3 px-8 rounded-full font-semibold 
                     hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300
                     hover:scale-105 hover:shadow-lg"
          >
            Contact Us
          </a>
        </div>
      </div>
    </div>
  );
}

export default FAQ; 