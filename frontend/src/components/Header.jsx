// Navbar component

import React, { useState, useEffect, useRef } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faBars,
  faHouse,
  faUsers,
  faEnvelope,
  faQuestionCircle,
  faTimes,
  faLightbulb,
  faCamera,
  faCogs,
} from "@fortawesome/free-solid-svg-icons";
import logo from "../assets/checkmaid-logo.png";
import { Link, useLocation } from "react-router-dom";

function Header({ onBookClick }) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [underlineStyle, setUnderlineStyle] = useState({});
  const location = useLocation();
  const navRef = useRef(null);

  // Navigation items configuration
  const navItems = [
    { path: '/', label: 'Home', icon: faHouse },
    { path: '/about', label: 'About Us', icon: faUsers },
    { path: '/our-work', label: 'Our Work', icon: faCamera },
    { path: '/how-it-works', label: 'How It Works', icon: faCogs },
    { path: '/faq', label: 'FAQ', icon: faQuestionCircle },
    { path: '/tips', label: 'Tips', icon: faLightbulb },
    { path: '/contact', label: 'Contact', icon: faEnvelope },
  ];

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // Update underline position when route changes
  useEffect(() => {
    const updateUnderline = () => {
      if (!navRef.current) return;

      const activeLink = navRef.current.querySelector(`[data-path="${location.pathname}"]`);
      if (activeLink) {
        const navContainer = navRef.current;
        const containerRect = navContainer.getBoundingClientRect();
        const linkRect = activeLink.getBoundingClientRect();

        setUnderlineStyle({
          width: linkRect.width,
          left: linkRect.left - containerRect.left,
          opacity: 1,
        });
      } else {
        setUnderlineStyle({ opacity: 0 });
      }
    };

    // Small delay to ensure DOM is ready
    const timer = setTimeout(updateUnderline, 50);

    // Update on window resize
    window.addEventListener('resize', updateUnderline);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', updateUnderline);
    };
  }, [location.pathname]);

  return (
    <div className="h-20 flex justify-between items-center bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
      {/* Navbar Logo */}
      <Link to="/" className="h-20 pl-12 flex items-center">
        <img
          src={logo}
          alt="CheckMaid Logo"
          className="h-[80%] max-h-full w-auto object-contain"
        />
      </Link>

      {/* Desktop Navigation */}
      <div className="hidden md:flex items-center space-x-8 pr-8">
        <nav ref={navRef} className="relative flex items-center space-x-8">
          {/* Animated Underline */}
          <div
            className="absolute bottom-0 h-0.5 bg-emerald-600 transition-all duration-300 ease-out rounded-full"
            style={{
              width: underlineStyle.width || 0,
              left: underlineStyle.left || 0,
              opacity: underlineStyle.opacity || 0,
              transform: 'translateY(8px)',
            }}
          />

          {/* Navigation Links */}
          {navItems.map((item) => {
            const isActive = location.pathname === item.path;
            return (
              <Link
                key={item.path}
                to={item.path}
                data-path={item.path}
                className={`relative flex items-center py-2 px-1 transition-colors duration-300 font-medium ${
                  isActive
                    ? 'text-emerald-600'
                    : 'text-gray-700 hover:text-emerald-600'
                }`}
              >
                <FontAwesomeIcon
                  icon={item.icon}
                  size="md"
                  className={`mr-2 transition-colors duration-300 ${
                    isActive ? 'text-emerald-600' : 'text-emerald-500'
                  }`}
                />
                <span>{item.label}</span>
              </Link>
            );
          })}
        </nav>

        <button
          className="bg-emerald-600 text-white px-6 py-2.5 rounded-lg font-medium hover:bg-emerald-700 transition-colors duration-300 ml-4"
          onClick={onBookClick}
        >
          Book Your Clean
        </button>
      </div>

      {/* Mobile Menu Button */}
      <button
        className="md:hidden mr-6 text-gray-700 hover:text-emerald-600 transition-colors duration-300"
        onClick={toggleMenu}
        aria-label="Toggle menu"
      >
        <FontAwesomeIcon icon={isMenuOpen ? faTimes : faBars} size="xl" />
      </button>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="fixed top-20 right-0 w-64 bg-white shadow-xl z-50 md:hidden">
          <div className="flex flex-col p-4 space-y-4">
            {navItems.map((item) => {
              const isActive = location.pathname === item.path;
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={() => setIsMenuOpen(false)}
                  className={`relative flex items-center p-2 rounded-lg transition-all duration-300 font-medium ${
                    isActive
                      ? 'text-emerald-600 bg-emerald-50 border-l-4 border-emerald-600'
                      : 'text-gray-700 hover:text-emerald-600 hover:bg-emerald-50'
                  }`}
                >
                  <FontAwesomeIcon
                    icon={item.icon}
                    size="md"
                    className={`mr-3 transition-colors duration-300 ${
                      isActive ? 'text-emerald-600' : 'text-emerald-500'
                    }`}
                  />
                  <span>{item.label}</span>
                </Link>
              );
            })}
            <button
              className="bg-emerald-600 text-white px-6 py-2.5 rounded-lg font-medium hover:bg-emerald-700 transition-colors duration-300 mt-4"
              onClick={() => {
                onBookClick();
                setIsMenuOpen(false);
              }}
            >
              Book Your Clean
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default Header;
