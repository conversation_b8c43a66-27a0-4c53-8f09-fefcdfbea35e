import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCalendarDay,
  faCalendarWeek,
  faCalendarAlt,
  faCalendarCheck
} from '@fortawesome/free-solid-svg-icons';

const frequencies = [
  {
    id: 'Weekly',
    name: 'Weekly Cleaning',
    description: 'Same day every week',
    icon: faCalendarWeek,
    multiplier: 0.9,
    price: 'From $90/visit'
  },
  {
    id: 'BiWeekly',
    name: 'Bi-Weekly Cleaning',
    description: 'Every other week',
    icon: faCalendarAlt,
    multiplier: 0.95,
    price: 'From $100-110/visit'
  },
  {
    id: 'Monthly',
    name: 'Monthly Cleaning',
    description: 'Once per month',
    icon: faCalendarCheck,
    multiplier: 1.0,
    price: 'From $120-135/visit'
  }
];

const FrequencySelection = ({ onSelect, selectedFrequency }) => {
  return (
    <div className="w-full">
      <label className="block text-sm font-medium text-gray-700 mb-4">
        Cleaning Frequency
      </label>
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        {frequencies.map((frequency) => (
          <button
            key={frequency.id}
            onClick={() => onSelect(frequency.id)}
            className={`p-6 border-2 rounded-xl text-left transition-all duration-300
              ${selectedFrequency === frequency.id
                ? 'border-emerald-500 bg-emerald-50 shadow-lg'
                : 'border-gray-200 hover:border-emerald-300 hover:bg-emerald-50/50 hover:shadow-md'
              }`}
          >
            <div className="flex items-start gap-4">
              <div className={`p-3 rounded-lg ${
                selectedFrequency === frequency.id 
                  ? 'bg-emerald-100 text-emerald-600' 
                  : 'bg-gray-100 text-gray-600'
              }`}>
                <FontAwesomeIcon
                  icon={frequency.icon}
                  className="text-xl"
                />
              </div>
              <div className="flex-1">
                <h3 className={`font-semibold text-lg mb-1 ${
                  selectedFrequency === frequency.id ? 'text-emerald-800' : 'text-gray-800'
                }`}>
                  {frequency.name}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  {frequency.description}
                </p>
                <p className="text-sm font-medium text-emerald-600">
                  {frequency.price}
                </p>
                {frequency.multiplier < 1 && (
                  <p className="text-xs font-medium text-emerald-500 mt-1">
                    Save {Math.round((1 - frequency.multiplier) * 100)}% with recurring service
                  </p>
                )}
              </div>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default FrequencySelection; 