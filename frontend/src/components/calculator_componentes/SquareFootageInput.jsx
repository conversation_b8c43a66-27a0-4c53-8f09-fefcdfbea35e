import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRuler } from '@fortawesome/free-solid-svg-icons';

const SquareFootageInput = ({ sqft, onChange }) => {
    const [inputValue, setInputValue] = useState(sqft);

    const handleInputChange = (e) => {
        const value = parseInt(e.target.value);
        if (!isNaN(value)) {
            const clampedValue = Math.min(Math.max(value, 500), 15000);
            setInputValue(clampedValue);
            onChange(clampedValue);
        }
    };

    const handleSliderChange = (e) => {
        const value = parseInt(e.target.value);
        setInputValue(value);
        onChange(value);
    };

    return (
        <div className="w-full">
            <label className="block text-sm font-medium text-gray-700 mb-4">
                Square Footage
            </label>
            <div className="space-y-4">
                <div className="relative">
                    <input
                        type="number"
                        value={inputValue}
                        onChange={handleInputChange}
                        min="500"
                        max="15000"
                        className="w-full p-4 border-2 border-gray-200 rounded-xl focus:border-emerald-500 focus:ring-2 focus:ring-emerald-200 transition-all duration-200"
                        placeholder="Enter square footage"
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <FontAwesomeIcon icon={faRuler} className="text-xl" />
                    </div>
                </div>
                <div className="flex items-center gap-4">
                    <input
                        type="range"
                        value={inputValue}
                        onChange={handleSliderChange}
                        min="500"
                        max="15000"
                        step="100"
                        className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <span className="text-sm font-medium text-gray-600 min-w-[80px]">
                        {inputValue.toLocaleString()} sq ft
                    </span>
                </div>
                <div className="flex justify-between text-xs text-gray-500">
                    <span>500 sq ft</span>
                    <span>15,000 sq ft</span>
                </div>
            </div>
        </div>
    );
};

export default SquareFootageInput;