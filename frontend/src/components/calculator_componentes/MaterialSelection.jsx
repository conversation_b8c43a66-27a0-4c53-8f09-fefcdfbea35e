import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHouse,
  faBroom,
  faBoxOpen,
  faBuilding,
  faHammer,
  faKey
} from '@fortawesome/free-solid-svg-icons';

const services = [
  {
    id: 'Standard',
    name: 'Standard / Recurring Home Cleaning',
    description: 'Weekly, bi-weekly, or monthly maintenance',
    icon: faHouse,
    basePrice: 'From $90/visit',
    pricing: {
      weekly: 'From $90/visit',
      biweekly: 'From $100-110/visit',
      monthly: 'From $120-135/visit',
      firstTime: '+$30-50 (optional deep touch)'
    }
  },
  {
    id: 'DeepClean',
    name: 'Deep Cleaning',
    description: 'One-time thorough cleaning',
    icon: faBroom,
    basePrice: 'From $200',
    pricing: {
      small: '~$200 (<1,000 sqft)',
      medium: '~$250 (1,000-2,000 sqft)',
      large: '$300+ (2,000+ sqft)'
    }
  },
  {
    id: 'MoveInOut',
    name: 'Move In/Out Cleaning',
    description: 'Complete reset cleaning',
    icon: faBoxOpen,
    basePrice: 'Starting at $250',
    pricing: {
      base: 'Starting at $250',
      includes: 'Full interior, baseboards, appliances',
      addons: '+Window cleaning or carpet shampoo'
    }
  },
  {
    id: 'Commercial',
    name: 'Commercial Cleaning',
    description: 'Office and workspace cleaning',
    icon: faBuilding,
    basePrice: 'From $150/visit',
    pricing: {
      office: '$150-200/visit (1,000-2,000 sqft)',
      large: 'Custom quotes ($0.08-0.12/sqft)',
      recurring: 'Monthly discounts available'
    }
  },
  {
    id: 'PostConstruction',
    name: 'Post-Construction Cleaning',
    description: 'Clean-up after renovations',
    icon: faHammer,
    basePrice: '$0.15-0.25/sqft',
    pricing: {
      rate: '$0.15-0.25/sqft',
      example: '1,500 sqft = ~$300-375',
      addons: 'Glass, paint removal available'
    }
  },
  {
    id: 'VacationRental',
    name: 'Vacation Rental / Airbnb Turnover',
    description: 'Fast cleans between guest stays',
    icon: faKey,
    basePrice: 'From $100',
    pricing: {
      oneBed: 'From $100',
      twoBed: 'From $120-140',
      threeBed: 'From $150+',
      addons: '+$15-25 for linen & restock'
    }
  }
];

const MaterialSelection = ({ onSelect, selectedServices }) => {
  const handleServiceClick = (serviceId) => {
    onSelect(serviceId);
  };

  return (
    <div className="w-full">
      <label className="block text-sm font-medium text-gray-700 mb-4">
        Select Services (Choose one or more)
      </label>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {services.map((service) => (
          <button
            key={service.id}
            type="button"
            onClick={() => handleServiceClick(service.id)}
            className={`p-6 border-2 rounded-xl text-left transition-all duration-300 cursor-pointer
              ${selectedServices.includes(service.id)
                ? 'border-emerald-500 bg-emerald-50 shadow-lg'
                : 'border-gray-200 hover:border-emerald-300 hover:bg-emerald-50/50 hover:shadow-md'
              }`}
          >
            <div className="flex items-start gap-4">
              <div className={`p-3 rounded-lg ${
                selectedServices.includes(service.id)
                  ? 'bg-emerald-100 text-emerald-600' 
                  : 'bg-gray-100 text-gray-600'
              }`}>
                <FontAwesomeIcon
                  icon={service.icon}
                  className="text-xl"
                />
              </div>
              <div className="flex-1">
                <h3 className={`font-semibold text-lg mb-1 ${
                  selectedServices.includes(service.id) ? 'text-emerald-800' : 'text-gray-800'
                }`}>
                  {service.name}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  {service.description}
                </p>
                <p className="text-sm font-medium text-emerald-600">
                  {service.basePrice}
                </p>
                {selectedServices.includes(service.id) && (
                  <div className="mt-2 text-xs font-medium text-emerald-600">
                    ✓ Selected
                  </div>
                )}
              </div>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default MaterialSelection;
