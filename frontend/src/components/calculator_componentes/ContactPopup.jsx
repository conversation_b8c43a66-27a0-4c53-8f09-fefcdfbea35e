import React, { useState } from 'react';

const ContactPopup = ({ onClose, price, materialType, sqft, isServiceSelected }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    message: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      // Handle form submission
      console.log('Form submitted:', formData);
      onClose();
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-gray-800">Contact Information 📝</h3>
            <div className="space-y-2">
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Your Name 👤"
                className="w-full p-2 border rounded"
                required
              />
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Email Address 📧"
                className="w-full p-2 border rounded"
                required
              />
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                placeholder="Phone Number 📱"
                className="w-full p-2 border rounded"
                required
              />
            </div>
          </div>
        );
      case 2:
        return (
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-gray-800">Service Details 🏠</h3>
            <div className="space-y-2">
              <input
                type="text"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                placeholder="Service Address 📍"
                className="w-full p-2 border rounded"
                required
              />
              <textarea
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                placeholder="Additional Notes 📝"
                className="w-full p-2 border rounded"
                rows="3"
              />
            </div>
          </div>
        );
      case 3:
        return (
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-gray-800">Review & Confirm ✅</h3>
            <div className="space-y-2 text-left">
              <p><strong>Service Type:</strong> {materialType} 🧹</p>
              <p><strong>Square Footage:</strong> {sqft} sq ft 📏</p>
              <p><strong>Instant Service:</strong> {isServiceSelected ? 'Yes ⚡' : 'No'}</p>
              <p><strong>Total Estimate:</strong> ${price} 💎</p>
              <div className="border-t border-gray-200 my-2"></div>
              <p><strong>Name:</strong> {formData.name}</p>
              <p><strong>Email:</strong> {formData.email}</p>
              <p><strong>Phone:</strong> {formData.phone}</p>
              <p><strong>Address:</strong> {formData.address}</p>
              {formData.message && <p><strong>Notes:</strong> {formData.message}</p>}
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-8 rounded-lg max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-gray-800">
            Book Your Service {currentStep === 3 ? '✅' : '📅'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ❌
          </button>
        </div>
        
        {renderStep()}
        
        <div className="mt-6 flex justify-between">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            Cancel ❌
          </button>
          <button
            onClick={handleNextStep}
            className="px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors"
          >
            {currentStep === 3 ? 'Confirm Booking 🚀' : 'Next Step ➡️'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ContactPopup;