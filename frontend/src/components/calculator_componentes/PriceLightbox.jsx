import React from 'react';

const PriceLightbox = ({ price, materialType, sqft, instantService, onClose, onFinalize }) => {
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-8 rounded-lg max-w-md w-full mx-4">
                <h2 className="text-2xl font-bold theme-text-color mb-4">
                    Your Quote Estimate
                </h2>
                <div className="space-y-4">
                    <div className="flex justify-between">
                        <span className="text-gray-600">Service Type:</span>
                        <span className="font-medium">{materialType}</span>
                    </div>
                    <div className="flex justify-between">
                        <span className="text-gray-600">Square Footage:</span>
                        <span className="font-medium">{sqft} sq ft</span>
                    </div>
                    {instantService && (
                        <div className="flex justify-between">
                            <span className="text-gray-600">Instant Service:</span>
                            <span className="font-medium">+$20</span>
                        </div>
                    )}
                    <div className="border-t border-gray-200 my-4"></div>
                    <div className="flex justify-between text-xl font-bold">
                        <span>Total Estimate:</span>
                        <span className="theme-text-color">${price}</span>
                    </div>
                </div>
                <div className="mt-6 flex justify-end gap-4">
                    <button
                        onClick={onClose}
                        className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={onFinalize}
                        className="px-4 py-2 theme-background-color text-white rounded-lg hover:opacity-90 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg"
                    >
                        Book Now
                    </button>
                </div>
            </div>
        </div>
    );
};

export default PriceLightbox;