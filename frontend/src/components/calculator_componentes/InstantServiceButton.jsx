// import React from 'react';
// import CleaningFrequency from './CleaningFrequency';
// const InstantServiceButton = ({ isSelected, onClick }) => {
//     return (
//         <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
//             <div>
//             <CleaningFrequency/>
//             </div>
//             <div>
//             <label className="block font-medium text-white text-lg mb-2 text-center">Instant Service</label>
//             <button
//                 type="button"
//                 onClick={onClick}
//                 className={`bg-gray-800 font-bold h-12 w-60 rounded-md border border-white ${isSelected ? 'bg-theme-color text-white' : ''}`}
//             >
//                 <span className="text-lg font-semibold">Instant Cleaning +$20</span>
//             </button>
//             </div>
//         </div>
//     );
// };

// export default InstantServiceButton;



import React from 'react';
import CleaningFrequency from './CleaningFrequency';

const InstantServiceButton = ({ isSelected, onClick }) => {
    return (
        <div className="w-full">
            <button
                type="button"
                onClick={onClick}
                className={`w-full p-4 border-2 rounded-lg text-left transition-all duration-200
                    ${isSelected
                        ? 'border-emerald-500 bg-emerald-50'
                        : 'border-gray-200 hover:border-emerald-300 hover:bg-emerald-50/50'
                    }`}
            >
                <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                        <span className="text-2xl">⚡</span>
                    </div>
                    <div>
                        <h3 className={`font-medium ${isSelected ? 'text-emerald-800' : 'text-gray-800'}`}>
                            Instant Service Option
                        </h3>
                        <p className="text-sm text-gray-500">
                            {isSelected ? 'Selected! 🎉' : 'Add $20 for same-day service 🚀'}
                        </p>
                    </div>
                </div>
            </button>
        </div>
    );
};

export default InstantServiceButton;
