import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faExclamationTriangle,
  faThumbsDown,
  faCommentDots,
  faHeadset
} from '@fortawesome/free-solid-svg-icons';

const ContactFormFields = ({ formData, handleChange, errors = {} }) => {
  const concernTypes = [
    { id: 'complaint', label: 'Complaint', icon: faThumbsDown },
    { id: 'feedback', label: 'Feedback', icon: faCommentDots },
    { id: 'service-issue', label: 'Service Issue', icon: faExclamationTriangle },
    { id: 'general', label: 'General Inquiry', icon: faHeadset }
  ];

  const handleConcernTypeChange = (concernType) => {
    handleChange({ target: { name: 'concernType', value: concernType } });
  };

  return (
    <>
      {/* Concern Type Selection */}
      <div>
        <label className="block text-gray-700 text-sm font-bold mb-2">
          What type of concern do you have? *
        </label>
        <div className="grid grid-cols-2 gap-4">
          {concernTypes.map((type) => (
            <button
              key={type.id}
              type="button"
              onClick={() => handleConcernTypeChange(type.id)}
              className={`p-4 rounded-lg border-2 transition-all duration-300 text-left
                ${formData.concernType === type.id 
                  ? 'border-emerald-500 bg-emerald-50' 
                  : 'border-gray-200 hover:border-emerald-300'}`}
            >
              <div className="flex items-center gap-3">
                <FontAwesomeIcon 
                  icon={type.icon} 
                  className={`text-xl ${formData.concernType === type.id ? 'text-emerald-600' : 'text-gray-400'}`} 
                />
                <span className="font-medium">{type.label}</span>
              </div>
            </button>
          ))}
        </div>
        {errors.concernType && (
          <p className="text-red-500 text-sm mt-1">{errors.concernType}</p>
        )}
      </div>

      {/* Personal Information */}
      <div className="grid md:grid-cols-2 gap-4">
        <div>
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="name">
            Full Name *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 ${
              errors.name ? 'border-red-500' : 'border-gray-300'
            }`}
            required
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name}</p>
          )}
        </div>

        <div>
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="email">
            Email Address *
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 ${
              errors.email ? 'border-red-500' : 'border-gray-300'
            }`}
            required
          />
          {errors.email && (
            <p className="text-red-500 text-sm mt-1">{errors.email}</p>
          )}
        </div>
      </div>

      <div>
        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="phone">
          Phone Number *
        </label>
        <input
          type="tel"
          id="phone"
          name="phone"
          value={formData.phone}
          onChange={handleChange}
          className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 ${
            errors.phone ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="(*************"
          required
        />
        {errors.phone && (
          <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
        )}
      </div>

      {/* Subject and Message */}
      <div>
        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="subject">
          Subject *
        </label>
        <input
          type="text"
          id="subject"
          name="subject"
          value={formData.subject}
          onChange={handleChange}
          className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 ${
            errors.subject ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="Brief description of your concern"
          required
        />
        {errors.subject && (
          <p className="text-red-500 text-sm mt-1">{errors.subject}</p>
        )}
      </div>

      <div>
        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="message">
          Message *
        </label>
        <textarea
          id="message"
          name="message"
          value={formData.message}
          onChange={handleChange}
          className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 ${
            errors.message ? 'border-red-500' : 'border-gray-300'
          }`}
          rows="4"
          placeholder="Please provide detailed information about your concern..."
          required
        />
        {errors.message && (
          <p className="text-red-500 text-sm mt-1">{errors.message}</p>
        )}
      </div>

      {/* Service-Related Information */}
      <div className="grid md:grid-cols-2 gap-4">
        <div>
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="serviceDate">
            Service Date (if applicable)
          </label>
          <input
            type="date"
            id="serviceDate"
            name="serviceDate"
            value={formData.serviceDate}
            onChange={handleChange}
            className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 ${
              errors.serviceDate ? 'border-red-500' : 'border-gray-300'
            }`}
          />
          {errors.serviceDate && (
            <p className="text-red-500 text-sm mt-1">{errors.serviceDate}</p>
          )}
        </div>

        <div>
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="referenceNumber">
            Reference Number (if available)
          </label>
          <input
            type="text"
            id="referenceNumber"
            name="referenceNumber"
            value={formData.referenceNumber}
            onChange={handleChange}
            className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 ${
              errors.referenceNumber ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Booking or invoice number"
          />
          {errors.referenceNumber && (
            <p className="text-red-500 text-sm mt-1">{errors.referenceNumber}</p>
          )}
        </div>
      </div>

      <div>
        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="serviceLocation">
          Service Location
        </label>
        <input
          type="text"
          id="serviceLocation"
          name="serviceLocation"
          value={formData.serviceLocation}
          onChange={handleChange}
          className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 ${
            errors.serviceLocation ? 'border-red-500' : 'border-gray-300'
          }`}
          placeholder="Address where service was provided"
        />
        {errors.serviceLocation && (
          <p className="text-red-500 text-sm mt-1">{errors.serviceLocation}</p>
        )}
      </div>

      {/* Preferred Contact Method */}
      <div>
        <label className="block text-gray-700 text-sm font-bold mb-2">
          Preferred Contact Method
        </label>
        <div className="flex gap-4">
          <label className="flex items-center">
            <input
              type="radio"
              name="preferredContact"
              value="email"
              checked={formData.preferredContact === 'email'}
              onChange={handleChange}
              className="mr-2"
            />
            Email
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="preferredContact"
              value="phone"
              checked={formData.preferredContact === 'phone'}
              onChange={handleChange}
              className="mr-2"
            />
            Phone
          </label>
        </div>
        {errors.preferredContact && (
          <p className="text-red-500 text-sm mt-1">{errors.preferredContact}</p>
        )}
      </div>
    </>
  );
};

export default ContactFormFields;
