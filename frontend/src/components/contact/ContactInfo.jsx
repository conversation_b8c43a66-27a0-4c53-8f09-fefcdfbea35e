import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faEnvelope, 
  faPhone, 
  faMapMarkerAlt, 
  faClock 
} from '@fortawesome/free-solid-svg-icons';

const ContactInfo = () => {
  const contactDetails = [
    {
      icon: faPhone,
      title: 'Phone',
      content: (
        <a 
          href="tel:+13609691270" 
          className="text-emerald-600 hover:text-emerald-500 transition-colors"
        >
          (*************
        </a>
      )
    },
    {
      icon: faEnvelope,
      title: 'Email',
      content: (
        <a 
          href="mailto:<EMAIL>" 
          className="text-emerald-600 hover:text-emerald-500 transition-colors"
        >
          <EMAIL>
        </a>
      )
    },
    {
      icon: faMapMarkerAlt,
      title: 'Location',
      content: <p className="text-gray-600">Serving the Greater Vancouver Area</p>
    },
    {
      icon: faClock,
      title: 'Hours',
      content: (
        <div className="text-gray-600">
          <p>Monday - Friday: 8:00 AM - 6:00 PM</p>
          <p>Saturday: 9:00 AM - 4:00 PM</p>
          <p>Sunday: Closed</p>
        </div>
      )
    }
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">Get in Touch</h2>
      <div className="space-y-6">
        {contactDetails.map((detail, index) => (
          <div key={index} className="flex items-start gap-4">
            <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
              <FontAwesomeIcon icon={detail.icon} className="text-emerald-600 text-xl" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-800">{detail.title}</h3>
              {detail.content}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ContactInfo;
