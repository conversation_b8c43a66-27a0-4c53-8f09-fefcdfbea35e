import { faTimes } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import React, { useState } from 'react';
import toast from 'react-hot-toast';

const BookingPopup = ({
  isOpen,
  onClose,
}) => {
  const [quoteForm, setQuoteForm] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    homeSize: '',
    preferredTime: '',
    cleaningType: '',
    addOns: []
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [showPrice, setShowPrice] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setQuoteForm(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user makes changes
    if (error) setError(null);
  };

  const getSquareFootage = (homeSize) => {
    switch(homeSize) {
      case 'small':
        return 900;
      case 'medium':
        return 1500;
      case 'large':
        return 2500;
      default:
        return 0;
    }
  };

  const calculatePrice = (cleaningType, homeSize, addOns = []) => {
    const sqft = getSquareFootage(homeSize);
    let basePrice = 0;

    switch(cleaningType) {
      case 'light':
        basePrice = 131;
        if (sqft > 900) {
          basePrice += (sqft - 900) * 0.144;
        }
        break;
      case 'medium':
        basePrice = 239;
        if (sqft > 1300) {
          basePrice += (sqft - 1300) * 0.216;
        }
        break;
      case 'deep':
        basePrice = 335;
        if (sqft > 1800) {
          basePrice += (sqft - 1800) * 0.3;
        }
        break;
    }

    // Add $36 for each add-on
    const addOnsTotal = addOns.length * 36;
    const subtotal = basePrice + addOnsTotal;

    // Apply 10% online booking discount
    const discount = subtotal * 0.1;
    const totalPrice = subtotal - discount;

    return {
      basePrice: Math.round(basePrice),
      addOnsTotal,
      discount: Math.round(discount),
      subtotal: Math.round(subtotal),
      totalPrice: Math.round(totalPrice)
    };
  };

  const getPriceRange = (cleaningType, homeSize, addOns = []) => {
    const { totalPrice } = calculatePrice(cleaningType, homeSize, addOns);
    return `$${totalPrice}`;
  };

  const getPriceBreakdown = (cleaningType, homeSize, addOns = []) => {
    const { totalPrice, discount, subtotal, basePrice, addOnsTotal } = calculatePrice(cleaningType, homeSize, addOns);
    return {
      totalPrice,
      discount,
      subtotal,
      basePrice,
      addOnsTotal
    };
  };

  const getCleaningDescription = (cleaningType) => {
    switch(cleaningType) {
      case 'light':
        return 'Great for recurring or upkeep. Includes dusting, vacuuming, mopping, countertop cleaning, light bathroom touch-up, and trash removal. Good for tidy homes that just need freshening up.';
      case 'medium':
        return 'Most popular option. Includes everything in Light Cleaning plus full kitchen wipe-down, bathroom scrub, baseboards, and room tidying. Ideal for regular monthly or move-in/out cleaning.';
      case 'deep':
        return 'Perfect for a total reset or neglected spaces. Includes everything in Standard plus inside appliances, cabinet cleaning, window sills, ceiling fans, and heavy grime removal.';
      default:
        return '';
    }
  };

  const handleQuoteSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // For demo purposes, simulate a successful submission
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Show price
      setShowPrice(true);
    } catch (error) {
      console.error('Error submitting quote:', error);
      setError('Unable to get quote at this time. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBookingSubmit = async () => {
    setIsSubmitting(true);
    setError(null);

    try {

      // Send data to backend
      const response = await fetch('http://localhost:8000/api/v1/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contactName: quoteForm.name,
          contactEmail: quoteForm.email,
          contactPhone: quoteForm.phone,
          serviceType: quoteForm.serviceType,
          packageType: quoteForm.homeSize,
          address: quoteForm.address,
          preferredDate: new Date().toISOString().split('T')[0],
          preferredTime: quoteForm.preferredTime,
          notes: `Cleaning Type: ${quoteForm.cleaningType}\nPrice: $${getPriceBreakdown(quoteForm.cleaningType, quoteForm.homeSize, quoteForm.addOns).totalPrice}\nAdd-ons: ${quoteForm.addOns.join(', ')}`,
          submittedAt: new Date().toISOString()
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error?.message || 'Failed to submit booking request');
      }

      console.log('Booking saved:', data);

      // Show success toast notification
      toast.success('🎉 Booking submitted successfully! We\'ll contact you soon to confirm your appointment.', {
        duration: 5000,
        style: {
          background: '#10b981',
          color: '#fff',
          fontWeight: '500',
          padding: '16px',
          borderRadius: '8px',
        },
        iconTheme: {
          primary: '#fff',
          secondary: '#10b981',
        },
      });

      onClose();
    } catch (error) {
      console.error('Error submitting booking:', error);

      // Show error toast notification
      toast.error('❌ Unable to submit booking. Please call us at (360) 969-1270 to complete your booking.', {
        duration: 6000,
        style: {
          background: '#ef4444',
          color: '#fff',
          fontWeight: '500',
          padding: '16px',
          borderRadius: '8px',
        },
        iconTheme: {
          primary: '#fff',
          secondary: '#ef4444',
        },
      });

      setError(
        <div className="space-y-2">
          <p className="font-medium">Unable to submit booking at this time.</p>
          <p>Error details: {error.message}</p>
          <p>Please call us directly at (360) 969-1270 to complete your booking.</p>
          <p className="text-sm text-gray-600">We apologize for any inconvenience.</p>
        </div>
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-100 px-4 sm:px-8 py-4 z-10">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl sm:text-2xl font-semibold text-gray-900">
                Get Your Instant Quote
              </h2>
              <p className="text-sm sm:text-base text-gray-500">
                Takes just 2 minutes to get your price
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors p-2 -m-2"
              disabled={isSubmitting}
            >
              <FontAwesomeIcon icon={faTimes} className="text-xl" />
            </button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleQuoteSubmit} className="p-4 sm:p-8 space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm">
              {error}
            </div>
          )}

          {showPrice ? (
            <div className="text-center py-4">
              <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-3 mb-3">
                <p className="text-sm text-emerald-700 font-medium">
                  🎉 You've unlocked 10% off with online booking!
                </p>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                <div className="text-left">
                  <h3 className="text-lg font-bold text-emerald-600 mb-2">
                    Your Estimated Price
                  </h3>
                  <p className="text-3xl font-bold text-gray-900 mb-2">
                    {getPriceRange(quoteForm.cleaningType, quoteForm.homeSize, quoteForm.addOns)}
                  </p>
                  <p className="text-sm text-gray-600">
                    {getCleaningDescription(quoteForm.cleaningType)}
                  </p>
                </div>
                <div className="bg-gray-50 rounded-lg p-3 text-left">
                  <h4 className="font-medium text-gray-900 mb-2 text-sm">Price Breakdown:</h4>
                  <div className="space-y-1 text-sm">
                    <p className="text-gray-600">
                      Base Price: ${getPriceBreakdown(quoteForm.cleaningType, quoteForm.homeSize).basePrice}
                    </p>
                    {quoteForm.addOns.length > 0 && (
                      <p className="text-gray-600">
                        Add-ons ({quoteForm.addOns.length}): +${quoteForm.addOns.length * 36}
                      </p>
                    )}
                    <p className="text-gray-600">
                      Subtotal: ${getPriceBreakdown(quoteForm.cleaningType, quoteForm.homeSize, quoteForm.addOns).subtotal}
                    </p>
                    <p className="text-emerald-600">
                      Online Discount (10%): -${getPriceBreakdown(quoteForm.cleaningType, quoteForm.homeSize, quoteForm.addOns).discount}
                    </p>
                    <p className="text-gray-900 font-medium pt-1 border-t border-gray-200">
                      Total: ${getPriceBreakdown(quoteForm.cleaningType, quoteForm.homeSize, quoteForm.addOns).totalPrice}
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2 mb-4">
                <p className="text-sm text-yellow-800">
                  ⓘ Prices are estimates and may vary after a detailed assessment.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-2 justify-center">
                <button
                  type="button"
                  onClick={handleBookingSubmit}
                  disabled={isSubmitting}
                  className="w-full sm:w-auto px-6 py-2.5 bg-sky-500 text-white text-sm font-medium rounded-lg hover:bg-sky-600 transition-all duration-300 focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 disabled:bg-sky-400 disabled:cursor-not-allowed flex items-center justify-center shadow-sm hover:shadow-md"
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </>
                  ) : (
                    "Let's Book It!"
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => setShowPrice(false)}
                  disabled={isSubmitting}
                  className="w-full sm:w-auto px-6 py-2.5 text-gray-600 hover:text-gray-700 text-sm font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  Adjust Quote
                </button>
              </div>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={quoteForm.name}
                    onChange={handleInputChange}
                    required
                    disabled={isSubmitting}
                    className="w-full px-4 py-2.5 text-base border border-gray-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed"
                    placeholder="John Doe"
                  />
                </div>

                <div>
                  <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={quoteForm.email}
                    onChange={handleInputChange}
                    required
                    disabled={isSubmitting}
                    className="w-full px-4 py-2.5 text-base border border-gray-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={quoteForm.phone}
                    onChange={handleInputChange}
                    required
                    disabled={isSubmitting}
                    className="w-full px-4 py-2.5 text-base border border-gray-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed"
                    placeholder="(*************"
                  />
                </div>

                <div>
                  <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                    Property Address *
                  </label>
                  <input
                    type="text"
                    name="address"
                    value={quoteForm.address}
                    onChange={handleInputChange}
                    required
                    disabled={isSubmitting}
                    className="w-full px-4 py-2.5 text-base border border-gray-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed"
                    placeholder="123 Main St, City, State ZIP"
                  />
                </div>

                <div>
                  <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                    Home Size *
                  </label>
                  <select
                    name="homeSize"
                    value={quoteForm.homeSize}
                    onChange={handleInputChange}
                    required
                    disabled={isSubmitting}
                    className="w-full px-4 py-2.5 text-base border border-gray-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed"
                  >
                    <option value="">Select home size</option>
                    <option value="small">Small (Under 1,500 sq ft)</option>
                    <option value="medium">Medium (1,500 - 2,500 sq ft)</option>
                    <option value="large">Large (Over 2,500 sq ft)</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                    Service Type *
                  </label>
                  <select
                    name="serviceType"
                    value={quoteForm.serviceType}
                    onChange={handleInputChange}
                    required
                    disabled={isSubmitting}
                    className="w-full px-4 py-2.5 text-base border border-gray-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed"
                  >
                    <option value="">Select service type</option>
                    <option value="residential">Residential</option>
                    <option value="commercial">Commercial</option>
                    <option value="airbnb">Airbnb</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                    Cleaning Type *
                  </label>
                  <select
                    name="cleaningType"
                    value={quoteForm.cleaningType}
                    onChange={handleInputChange}
                    required
                    disabled={isSubmitting}
                    className="w-full px-4 py-2.5 text-base border border-gray-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed"
                  >
                    <option value="">Select cleaning type</option>
                    <option value="light">Light Cleaning</option>
                    <option value="medium">Standard/Medium Cleaning</option>
                    <option value="deep">Deep Cleaning</option>
                  </select>
                </div>

                <div className="sm:col-span-1">
                  <label className="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                    Preferred Time *
                  </label>
                  <select
                    name="preferredTime"
                    value={quoteForm.preferredTime}
                    onChange={handleInputChange}
                    required
                    disabled={isSubmitting}
                    className="w-full px-4 py-2.5 text-base border border-gray-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed"
                  >
                    <option value="">Select preferred time</option>
                    <option value="morning">Morning (8AM - 12PM)</option>
                    <option value="afternoon">Afternoon (12PM - 4PM)</option>
                    <option value="evening">Evening (4PM - 7PM)</option>
                  </select>
                </div>
              </div>

              {quoteForm.cleaningType && (
                <p className="text-sm sm:text-base text-gray-600 mt-3">
                  {getCleaningDescription(quoteForm.cleaningType)}
                </p>
              )}

              {/* Submit Button */}
              <div className="pt-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-sky-500 text-white py-3 px-6 rounded-lg text-base font-medium hover:bg-sky-600 transition-all duration-300 focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 disabled:bg-sky-400 disabled:cursor-not-allowed flex items-center justify-center shadow-sm hover:shadow-md"
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </>
                  ) : (
                    'Get Instant Quote'
                  )}
                </button>
              </div>
            </>
          )}
        </form>
      </div>
    </div>
  );
};

export default BookingPopup;