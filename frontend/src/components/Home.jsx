import React from 'react';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faHome, 
  faBuilding, 
  faStar, 
  faShieldAlt, 
  faLeaf, 
  faUsers,
  faArrowRight,
  faCheckCircle
} from '@fortawesome/free-solid-svg-icons';

function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="relative h-[80vh] bg-gradient-to-r from-emerald-500 to-emerald-600">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative h-full flex items-center justify-center text-center text-white px-4">
          <div className="max-w-3xl">
            <div className="inline-flex items-center gap-2 bg-emerald-100/20 text-emerald-100 px-4 py-2 rounded-full mb-6">
              <FontAwesomeIcon icon={faStar} className="text-yellow-400" />
              <span className="font-semibold">Professional Cleaning Services</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 animate-fadeIn">
              Transform Your Space with Expert Cleaning
            </h1>
            <p className="text-xl md:text-2xl animate-fadeIn delay-200">
              Experience the difference of professional cleaning services
            </p>
          </div>
        </div>
      </div>

      {/* Trusted By Section */}
      <div className="bg-white py-16 sm:py-24">
        <div className="max-w-4xl mx-auto px-6 sm:px-8">
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-4 sm:mb-6">
              Trusted by Brands Like Applebee's
            </h2>
            <p className="text-base sm:text-lg text-gray-600">
              We provide reliable, professional outdoor cleaning for major national chains — including trusted names like Applebee's.
            </p>
          </div>

          <div className="bg-gray-50 rounded-2xl p-6 sm:p-10 shadow-sm">
            <div className="flex flex-col md:flex-row items-center gap-8 sm:gap-10">
              <div className="flex-shrink-0 w-full md:w-1/3">
                <img 
                  src="/applebees.png" 
                  alt="Applebee's Logo" 
                  className="w-full h-auto object-contain"
                />
              </div>
              <div className="flex-grow">
                <p className="text-base sm:text-lg text-gray-600 mb-6 sm:mb-8">
                  From leaf blowing and seasonal maintenance to commercial pressure washing, we help businesses stay clean, safe, and welcoming for customers.
                </p>
                <div className="space-y-3 sm:space-y-4">
                  <div className="flex items-center gap-3 sm:gap-4 text-base sm:text-lg text-gray-700">
                    <FontAwesomeIcon icon={faCheckCircle} className="text-emerald-500" />
                    <span>Commercial Pressure Washing</span>
                  </div>
                  <div className="flex items-center gap-3 sm:gap-4 text-base sm:text-lg text-gray-700">
                    <FontAwesomeIcon icon={faCheckCircle} className="text-emerald-500" />
                    <span>Leaf Blowing & Property Maintenance</span>
                  </div>
                  <div className="flex items-center gap-3 sm:gap-4 text-base sm:text-lg text-gray-700">
                    <FontAwesomeIcon icon={faCheckCircle} className="text-emerald-500" />
                    <span>Sidewalks, Patios, Building Exteriors</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Service Selection Section */}
      <div className="max-w-6xl mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">Select Your Service</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Choose the type of cleaning service that best fits your needs
          </p>
        </div>
        <div className="grid md:grid-cols-2 gap-8">
          {/* Residential Service */}
          <Link 
            to="/quote?service=residential" 
            className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300"
          >
            <div className="aspect-w-16 aspect-h-9">
              <img 
                src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
                alt="Residential Cleaning" 
                className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
                  <FontAwesomeIcon icon={faHome} className="text-emerald-600 text-xl" />
                </div>
                <h3 className="text-2xl font-semibold text-gray-800">Residential Cleaning</h3>
              </div>
              <p className="text-gray-600 mb-6">
                Professional cleaning services for your home, apartment, or condo. We handle everything from regular maintenance to deep cleaning.
              </p>
              <div className="flex items-center justify-between">
                <ul className="space-y-2">
                  <li className="flex items-center gap-2 text-gray-600">
                    <FontAwesomeIcon icon={faStar} className="text-emerald-500" />
                    <span>Regular & Deep Cleaning</span>
                  </li>
                  <li className="flex items-center gap-2 text-gray-600">
                    <FontAwesomeIcon icon={faLeaf} className="text-emerald-500" />
                    <span>Eco-Friendly Products</span>
                  </li>
                  <li className="flex items-center gap-2 text-gray-600">
                    <FontAwesomeIcon icon={faShieldAlt} className="text-emerald-500" />
                    <span>Insured & Bonded</span>
                  </li>
                </ul>
                <div className="w-10 h-10 rounded-full bg-emerald-100 flex items-center justify-center group-hover:bg-emerald-500 group-hover:text-white transition-colors duration-300">
                  <FontAwesomeIcon icon={faArrowRight} className="text-emerald-600 group-hover:text-white" />
                </div>
              </div>
            </div>
          </Link>

          {/* Commercial Service */}
          <Link 
            to="/quote?service=commercial" 
            className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300"
          >
            <div className="aspect-w-16 aspect-h-9">
              <img 
                src="https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80" 
                alt="Commercial Cleaning" 
                className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center flex-shrink-0">
                  <FontAwesomeIcon icon={faBuilding} className="text-emerald-600 text-xl" />
                </div>
                <h3 className="text-2xl font-semibold text-gray-800">Commercial Cleaning</h3>
              </div>
              <p className="text-gray-600 mb-6">
                Comprehensive cleaning solutions for offices, retail spaces, and commercial properties. Customized to your business needs.
              </p>
              <div className="flex items-center justify-between">
                <ul className="space-y-2">
                  <li className="flex items-center gap-2 text-gray-600">
                    <FontAwesomeIcon icon={faStar} className="text-emerald-500" />
                    <span>Customized Plans</span>
                  </li>
                  <li className="flex items-center gap-2 text-gray-600">
                    <FontAwesomeIcon icon={faUsers} className="text-emerald-500" />
                    <span>Professional Team</span>
                  </li>
                  <li className="flex items-center gap-2 text-gray-600">
                    <FontAwesomeIcon icon={faShieldAlt} className="text-emerald-500" />
                    <span>Insured & Bonded</span>
                  </li>
                </ul>
                <div className="w-10 h-10 rounded-full bg-emerald-100 flex items-center justify-center group-hover:bg-emerald-500 group-hover:text-white transition-colors duration-300">
                  <FontAwesomeIcon icon={faArrowRight} className="text-emerald-600 group-hover:text-white" />
                </div>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}

export default Home; 