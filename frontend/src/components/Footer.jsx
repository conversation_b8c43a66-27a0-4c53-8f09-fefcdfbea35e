import React from 'react';
import PropTypes from 'prop-types';
import logo from '../assets/checkmaid-logo.png';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPhone, faEnvelope } from '@fortawesome/free-solid-svg-icons';

const Footer = ({ logoUrl, companyName = "Checkmaid", year = new Date().getFullYear() }) => {
  return (
    <footer className="bg-white border-t border-gray-100">
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2 lg:col-span-3">
            <img 
              src={logoUrl || logo} 
              alt={`${companyName} Logo`} 
              className="h-12 mb-4"
              loading="lazy"
            />
            <p className="text-gray-600 mb-4 max-w-2xl">
              Professional cleaning services for your home and office. We bring our own supplies and equipment to each job.
            </p>
            <div className="flex items-center space-x-4 text-gray-600">
              <a href="tel:+13609691270" className="flex items-center hover:text-emerald-600 transition-colors">
                <FontAwesomeIcon icon={faPhone} className="mr-2" />
                (*************
              </a>
              <a href="mailto:<EMAIL>" className="flex items-center hover:text-emerald-600 transition-colors">
                <FontAwesomeIcon icon={faEnvelope} className="mr-2" />
                <EMAIL>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Company</h3>
            <div className="grid grid-cols-2 md:grid-cols-2 gap-4">
              <ul className="space-y-2">
                <li>
                  <Link to="/" className="text-gray-600 hover:text-emerald-600 transition-colors">
                    Home
                  </Link>
                </li>
                <li>
                  <Link to="/about" className="text-gray-600 hover:text-emerald-600 transition-colors">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link to="/our-work" className="text-gray-600 hover:text-emerald-600 transition-colors">
                    Our Work
                  </Link>
                </li>
              </ul>
              <ul className="space-y-2">
                <li>
                  <Link to="/how-it-works" className="text-gray-600 hover:text-emerald-600 transition-colors">
                    How It Works
                  </Link>
                </li>
                <li>
                  <Link to="/faq" className="text-gray-600 hover:text-emerald-600 transition-colors">
                    FAQ
                  </Link>
                </li>
                <li>
                  <Link to="/tips" className="text-gray-600 hover:text-emerald-600 transition-colors">
                    Tips
                  </Link>
                </li>
                <li>
                  <Link to="/contact" className="text-gray-600 hover:text-emerald-600 transition-colors">
                    Contact
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-100 mt-8 pt-8 text-center text-gray-600 text-sm">
          &copy; {year} {companyName}. All rights reserved.
        </div>
      </div>
    </footer>
  );
};

Footer.propTypes = {
  logoUrl: PropTypes.string,
  companyName: PropTypes.string,
  year: PropTypes.number
};

export default Footer;