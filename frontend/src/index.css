@import "tailwindcss";

:root {
    --primary-1: #00a8c4;
    --primary-2: #59ce64;
    --primary-3: #00bef2;
    --primary-4: #39b54a;
}

/* .theme-text-color {
    color: linear-gradient(270deg, #00a8c4, #59ce64, var(--primary-3), var(--primary-4));
   
    animation: gradientFlow 8s ease infinite;
}
@keyframes gradientFlow {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}
 */

/* .theme-background-color {
    background: linear-gradient(270deg, #00a8c4, #59ce64, var(--primary-3), var(--primary-4));
} */


/* Gradient Animated Text */
.theme-text-color {
    background: linear-gradient(270deg, #00a8c4, #59ce64, var(--primary-3), var(--primary-4));
    background-size: 300% 300%;
    -webkit-background-clip: text; /* For Webkit-based browsers */
    background-clip: text;
    color: transparent; /* Hide actual text color */
    animation: gradientFlow 8s ease infinite;
  }
  
  @keyframes gradientFlow {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  


.theme-background-color {
    background: linear-gradient(270deg, #00a8c4, #59ce64, var(--primary-3), var(--primary-4));
    background-size: 300% 300%; /* Make the gradient larger than the container */
    animation: gradientFlow 18s ease infinite;
}

@keyframes gradientFlow {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}
  
  
button {
    cursor: pointer;
}

