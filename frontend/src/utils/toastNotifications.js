/**
 * Toast Notifications Utility
 * Centralized toast notification configurations and functions
 */

import toast from 'react-hot-toast';

// Toast styling constants
export const TOAST_STYLES = {
  SUCCESS: {
    background: '#10b981',
    color: '#fff',
    fontWeight: '500',
    padding: '16px',
    borderRadius: '8px',
  },
  ERROR: {
    background: '#ef4444',
    color: '#fff',
    fontWeight: '500',
    padding: '16px',
    borderRadius: '8px',
  },
  WARNING: {
    background: '#f59e0b',
    color: '#fff',
    fontWeight: '500',
    padding: '16px',
    borderRadius: '8px',
  },
  INFO: {
    background: '#3b82f6',
    color: '#fff',
    fontWeight: '500',
    padding: '16px',
    borderRadius: '8px',
  },
};

export const TOAST_DURATIONS = {
  SHORT: 3000,
  MEDIUM: 4000,
  LONG: 5000,
  EXTRA_LONG: 6000,
};

/**
 * Show success toast for booking submission
 * @param {string} message - Custom message (optional)
 */
export const showBookingSuccessToast = (message) => {
  const defaultMessage = "🎉 Booking submitted successfully! We'll contact you soon to confirm your appointment.";
  
  toast.success(message || defaultMessage, {
    duration: TOAST_DURATIONS.LONG,
    style: TOAST_STYLES.SUCCESS,
    iconTheme: {
      primary: '#fff',
      secondary: '#10b981',
    },
  });
};

/**
 * Show error toast for booking submission failure
 * @param {string} message - Custom message (optional)
 */
export const showBookingErrorToast = (message) => {
  const defaultMessage = "❌ Unable to submit booking. Please call us at (360) 969-1270 to complete your booking.";
  
  toast.error(message || defaultMessage, {
    duration: TOAST_DURATIONS.EXTRA_LONG,
    style: TOAST_STYLES.ERROR,
    iconTheme: {
      primary: '#fff',
      secondary: '#ef4444',
    },
  });
};

/**
 * Show success toast for quote generation
 * @param {string} message - Custom message (optional)
 */
export const showQuoteSuccessToast = (message) => {
  const defaultMessage = "✅ Quote generated successfully!";
  
  toast.success(message || defaultMessage, {
    duration: TOAST_DURATIONS.MEDIUM,
    style: TOAST_STYLES.SUCCESS,
    iconTheme: {
      primary: '#fff',
      secondary: '#10b981',
    },
  });
};

/**
 * Show error toast for quote generation failure
 * @param {string} message - Custom message (optional)
 */
export const showQuoteErrorToast = (message) => {
  const defaultMessage = "❌ Unable to generate quote. Please try again.";
  
  toast.error(message || defaultMessage, {
    duration: TOAST_DURATIONS.LONG,
    style: TOAST_STYLES.ERROR,
    iconTheme: {
      primary: '#fff',
      secondary: '#ef4444',
    },
  });
};

/**
 * Show validation error toast
 * @param {string} message - Validation error message
 */
export const showValidationErrorToast = (message) => {
  toast.error(message, {
    duration: TOAST_DURATIONS.MEDIUM,
    style: TOAST_STYLES.ERROR,
    iconTheme: {
      primary: '#fff',
      secondary: '#ef4444',
    },
  });
};

/**
 * Show info toast
 * @param {string} message - Info message
 */
export const showInfoToast = (message) => {
  toast(message, {
    duration: TOAST_DURATIONS.MEDIUM,
    style: TOAST_STYLES.INFO,
    icon: 'ℹ️',
  });
};

/**
 * Show warning toast
 * @param {string} message - Warning message
 */
export const showWarningToast = (message) => {
  toast(message, {
    duration: TOAST_DURATIONS.MEDIUM,
    style: TOAST_STYLES.WARNING,
    icon: '⚠️',
  });
};

/**
 * Dismiss all toasts
 */
export const dismissAllToasts = () => {
  toast.dismiss();
};

/**
 * Show custom toast with specific configuration
 * @param {string} message - Toast message
 * @param {Object} options - Toast options
 */
export const showCustomToast = (message, options = {}) => {
  const defaultOptions = {
    duration: TOAST_DURATIONS.MEDIUM,
    style: TOAST_STYLES.INFO,
  };

  toast(message, { ...defaultOptions, ...options });
};
