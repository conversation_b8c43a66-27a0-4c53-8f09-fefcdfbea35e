/**
 * Cleaning Service Utility
 * Constants and descriptions for cleaning services
 */

export const CLEANING_TYPES = {
  LIGHT: 'light',
  MEDIUM: 'medium',
  DEEP: 'deep',
};

export const HOME_SIZES = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large',
};

export const SERVICE_TYPES = {
  RESIDENTIAL: 'residential',
  COMMERCIAL: 'commercial',
  AIRBNB: 'airbnb',
};

export const PREFERRED_TIMES = {
  MORNING: 'morning',
  AFTERNOON: 'afternoon',
  EVENING: 'evening',
};

/**
 * Get description for a cleaning type
 * @param {string} cleaningType - Type of cleaning service
 * @returns {string} Description of the cleaning service
 */
export const getCleaningDescription = (cleaningType) => {
  const descriptions = {
    [CLEANING_TYPES.LIGHT]: 
      'Great for recurring or upkeep. Includes dusting, vacuuming, mopping, countertop cleaning, light bathroom touch-up, and trash removal. Good for tidy homes that just need freshening up.',
    [CLEANING_TYPES.MEDIUM]: 
      'Most popular option. Includes everything in Light Cleaning plus full kitchen wipe-down, bathroom scrub, baseboards, and room tidying. Ideal for regular monthly or move-in/out cleaning.',
    [CLEANING_TYPES.DEEP]: 
      'Perfect for a total reset or neglected spaces. Includes everything in Standard plus inside appliances, cabinet cleaning, window sills, ceiling fans, and heavy grime removal.',
  };

  return descriptions[cleaningType] || '';
};

/**
 * Get display label for home size
 * @param {string} homeSize - Size of the home
 * @returns {string} Display label for home size
 */
export const getHomeSizeLabel = (homeSize) => {
  const labels = {
    [HOME_SIZES.SMALL]: 'Small (Under 1,500 sq ft)',
    [HOME_SIZES.MEDIUM]: 'Medium (1,500 - 2,500 sq ft)',
    [HOME_SIZES.LARGE]: 'Large (Over 2,500 sq ft)',
  };

  return labels[homeSize] || '';
};

/**
 * Get display label for service type
 * @param {string} serviceType - Type of service
 * @returns {string} Display label for service type
 */
export const getServiceTypeLabel = (serviceType) => {
  const labels = {
    [SERVICE_TYPES.RESIDENTIAL]: 'Residential',
    [SERVICE_TYPES.COMMERCIAL]: 'Commercial',
    [SERVICE_TYPES.AIRBNB]: 'Airbnb',
  };

  return labels[serviceType] || '';
};

/**
 * Get display label for preferred time
 * @param {string} preferredTime - Preferred time slot
 * @returns {string} Display label for preferred time
 */
export const getPreferredTimeLabel = (preferredTime) => {
  const labels = {
    [PREFERRED_TIMES.MORNING]: 'Morning (8AM - 12PM)',
    [PREFERRED_TIMES.AFTERNOON]: 'Afternoon (12PM - 4PM)',
    [PREFERRED_TIMES.EVENING]: 'Evening (4PM - 7PM)',
  };

  return labels[preferredTime] || '';
};

/**
 * Get all cleaning type options for select dropdown
 * @returns {Array} Array of cleaning type options
 */
export const getCleaningTypeOptions = () => [
  { value: '', label: 'Select cleaning type' },
  { value: CLEANING_TYPES.LIGHT, label: 'Light Cleaning' },
  { value: CLEANING_TYPES.MEDIUM, label: 'Standard/Medium Cleaning' },
  { value: CLEANING_TYPES.DEEP, label: 'Deep Cleaning' },
];

/**
 * Get all home size options for select dropdown
 * @returns {Array} Array of home size options
 */
export const getHomeSizeOptions = () => [
  { value: '', label: 'Select home size' },
  { value: HOME_SIZES.SMALL, label: getHomeSizeLabel(HOME_SIZES.SMALL) },
  { value: HOME_SIZES.MEDIUM, label: getHomeSizeLabel(HOME_SIZES.MEDIUM) },
  { value: HOME_SIZES.LARGE, label: getHomeSizeLabel(HOME_SIZES.LARGE) },
];

/**
 * Get all service type options for select dropdown
 * @returns {Array} Array of service type options
 */
export const getServiceTypeOptions = () => [
  { value: '', label: 'Select service type' },
  { value: SERVICE_TYPES.RESIDENTIAL, label: getServiceTypeLabel(SERVICE_TYPES.RESIDENTIAL) },
  { value: SERVICE_TYPES.COMMERCIAL, label: getServiceTypeLabel(SERVICE_TYPES.COMMERCIAL) },
  { value: SERVICE_TYPES.AIRBNB, label: getServiceTypeLabel(SERVICE_TYPES.AIRBNB) },
];

/**
 * Get all preferred time options for select dropdown
 * @returns {Array} Array of preferred time options
 */
export const getPreferredTimeOptions = () => [
  { value: '', label: 'Select preferred time' },
  { value: PREFERRED_TIMES.MORNING, label: getPreferredTimeLabel(PREFERRED_TIMES.MORNING) },
  { value: PREFERRED_TIMES.AFTERNOON, label: getPreferredTimeLabel(PREFERRED_TIMES.AFTERNOON) },
  { value: PREFERRED_TIMES.EVENING, label: getPreferredTimeLabel(PREFERRED_TIMES.EVENING) },
];
