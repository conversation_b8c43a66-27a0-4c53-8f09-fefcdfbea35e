/**
 * Pricing Calculator Utility
 * Pure functions for calculating cleaning service prices
 */

// Constants
export const HOME_SIZE_SQUARE_FOOTAGE = {
  small: 900,
  medium: 1500,
  large: 2500,
};

export const CLEANING_TYPE_CONFIG = {
  light: {
    basePrice: 131,
    threshold: 900,
    rate: 0.144,
  },
  medium: {
    basePrice: 239,
    threshold: 1300,
    rate: 0.216,
  },
  deep: {
    basePrice: 335,
    threshold: 1800,
    rate: 0.3,
  },
};

export const PRICING_CONSTANTS = {
  ADD_ON_PRICE: 36,
  ONLINE_DISCOUNT_RATE: 0.1,
};

/**
 * Get square footage for a given home size
 * @param {string} homeSize - 'small', 'medium', or 'large'
 * @returns {number} Square footage
 */
export const getSquareFootage = (homeSize) => {
  return HOME_SIZE_SQUARE_FOOTAGE[homeSize] || 0;
};

/**
 * Calculate base price for cleaning type and home size
 * @param {string} cleaningType - 'light', 'medium', or 'deep'
 * @param {string} homeSize - 'small', 'medium', or 'large'
 * @returns {number} Base price
 */
export const calculateBasePrice = (cleaningType, homeSize) => {
  const config = CLEANING_TYPE_CONFIG[cleaningType];
  if (!config) return 0;

  const sqft = getSquareFootage(homeSize);
  let basePrice = config.basePrice;

  if (sqft > config.threshold) {
    basePrice += (sqft - config.threshold) * config.rate;
  }

  return basePrice;
};

/**
 * Calculate total add-ons price
 * @param {Array} addOns - Array of add-on items
 * @returns {number} Total add-ons price
 */
export const calculateAddOnsTotal = (addOns = []) => {
  return addOns.length * PRICING_CONSTANTS.ADD_ON_PRICE;
};

/**
 * Calculate complete price breakdown
 * @param {string} cleaningType - Type of cleaning service
 * @param {string} homeSize - Size of the home
 * @param {Array} addOns - Array of add-on services
 * @returns {Object} Complete price breakdown
 */
export const calculatePriceBreakdown = (cleaningType, homeSize, addOns = []) => {
  const basePrice = calculateBasePrice(cleaningType, homeSize);
  const addOnsTotal = calculateAddOnsTotal(addOns);
  const subtotal = basePrice + addOnsTotal;
  const discount = subtotal * PRICING_CONSTANTS.ONLINE_DISCOUNT_RATE;
  const totalPrice = subtotal - discount;

  return {
    basePrice: Math.round(basePrice),
    addOnsTotal,
    discount: Math.round(discount),
    subtotal: Math.round(subtotal),
    totalPrice: Math.round(totalPrice),
  };
};

/**
 * Get formatted price string
 * @param {string} cleaningType - Type of cleaning service
 * @param {string} homeSize - Size of the home
 * @param {Array} addOns - Array of add-on services
 * @returns {string} Formatted price string
 */
export const getFormattedPrice = (cleaningType, homeSize, addOns = []) => {
  const { totalPrice } = calculatePriceBreakdown(cleaningType, homeSize, addOns);
  return `$${totalPrice}`;
};

/**
 * Validate pricing inputs
 * @param {string} cleaningType - Type of cleaning service
 * @param {string} homeSize - Size of the home
 * @returns {boolean} Whether inputs are valid
 */
export const validatePricingInputs = (cleaningType, homeSize) => {
  return Boolean(
    cleaningType && 
    homeSize && 
    CLEANING_TYPE_CONFIG[cleaningType] && 
    HOME_SIZE_SQUARE_FOOTAGE[homeSize]
  );
};
