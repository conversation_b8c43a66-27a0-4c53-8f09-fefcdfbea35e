/**
 * Custom Hook for Booking Form State Management
 */

import { useState, useCallback } from 'react';
import { validateBookingForm } from '../services/bookingService';

// Initial form state
const INITIAL_FORM_STATE = {
  name: '',
  email: '',
  phone: '',
  address: '',
  homeSize: '',
  preferredTime: '',
  cleaningType: '',
  serviceType: '',
  addOns: [],
};

/**
 * Custom hook for managing booking form state
 * @returns {Object} Form state and handlers
 */
export const useBookingForm = () => {
  const [formData, setFormData] = useState(INITIAL_FORM_STATE);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  /**
   * Handle input change
   * @param {Event} e - Input change event
   */
  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear error for this field when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  }, [errors]);

  /**
   * Handle field blur (for validation)
   * @param {Event} e - Blur event
   */
  const handleFieldBlur = useCallback((e) => {
    const { name } = e.target;
    setTouched(prev => ({
      ...prev,
      [name]: true,
    }));
  }, []);

  /**
   * Handle add-ons change
   * @param {Array} newAddOns - Updated add-ons array
   */
  const handleAddOnsChange = useCallback((newAddOns) => {
    setFormData(prev => ({
      ...prev,
      addOns: newAddOns,
    }));
  }, []);

  /**
   * Validate the entire form
   * @returns {boolean} Whether the form is valid
   */
  const validateForm = useCallback(() => {
    const validation = validateBookingForm(formData);
    setErrors(validation.errors);
    return validation.isValid;
  }, [formData]);

  /**
   * Reset form to initial state
   */
  const resetForm = useCallback(() => {
    setFormData(INITIAL_FORM_STATE);
    setErrors({});
    setTouched({});
  }, []);

  /**
   * Update form data programmatically
   * @param {Object} updates - Partial form data updates
   */
  const updateFormData = useCallback((updates) => {
    setFormData(prev => ({
      ...prev,
      ...updates,
    }));
  }, []);

  /**
   * Check if form has been modified
   * @returns {boolean} Whether form has changes
   */
  const hasChanges = useCallback(() => {
    return JSON.stringify(formData) !== JSON.stringify(INITIAL_FORM_STATE);
  }, [formData]);

  /**
   * Get field error message
   * @param {string} fieldName - Name of the field
   * @returns {string} Error message or empty string
   */
  const getFieldError = useCallback((fieldName) => {
    return touched[fieldName] ? errors[fieldName] || '' : '';
  }, [errors, touched]);

  /**
   * Check if field has error
   * @param {string} fieldName - Name of the field
   * @returns {boolean} Whether field has error
   */
  const hasFieldError = useCallback((fieldName) => {
    return Boolean(touched[fieldName] && errors[fieldName]);
  }, [errors, touched]);

  /**
   * Check if form is ready for submission
   * @returns {boolean} Whether form can be submitted
   */
  const isFormReady = useCallback(() => {
    const requiredFields = [
      'name', 'email', 'phone', 'address', 
      'homeSize', 'serviceType', 'cleaningType', 'preferredTime'
    ];
    
    return requiredFields.every(field => 
      formData[field] && formData[field].trim() !== ''
    );
  }, [formData]);

  return {
    // Form state
    formData,
    errors,
    touched,
    
    // Form handlers
    handleInputChange,
    handleFieldBlur,
    handleAddOnsChange,
    
    // Form utilities
    validateForm,
    resetForm,
    updateFormData,
    hasChanges,
    
    // Field utilities
    getFieldError,
    hasFieldError,
    isFormReady,
  };
};
