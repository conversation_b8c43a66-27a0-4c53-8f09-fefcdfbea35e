import { useState, useEffect } from 'react';
import testimonialService from '../services/testimonialService';

/**
 * Custom hook to manage review popup functionality
 * Automatically shows review popup after successful booking
 */
const useReviewPopup = () => {
  const [isReviewPopupOpen, setIsReviewPopupOpen] = useState(false);
  const [bookingData, setBookingData] = useState(null);
  const [reviewSubmitted, setReviewSubmitted] = useState(false);

  // Function to trigger review popup after successful booking
  const triggerReviewPopup = (booking) => {
    console.log('🎯 triggerReviewPopup called with booking:', booking);

    // Store booking data for the review form
    setBookingData(booking);

    console.log('⏰ Setting 2-second delay for review popup...');

    // Small delay to let the booking success message show first
    setTimeout(() => {
      console.log('🚀 Opening review popup now!');
      setIsReviewPopupOpen(true);
    }, 2000); // 2 second delay
  };

  // Function to close review popup
  const closeReviewPopup = () => {
    setIsReviewPopupOpen(false);
    setBookingData(null);
  };

  // Function to handle review submission
  const handleReviewSubmit = async (reviewData) => {
    try {
      // Submit the review/testimonial using public endpoint
      const response = await testimonialService.createPublicTestimonial(reviewData);

      if (response.success) {
        setReviewSubmitted(true);

        // Store in localStorage to prevent showing popup again for this booking
        if (bookingData?.bookingNumber) {
          const reviewedBookings = JSON.parse(localStorage.getItem('reviewedBookings') || '[]');
          reviewedBookings.push(bookingData.bookingNumber);
          localStorage.setItem('reviewedBookings', JSON.stringify(reviewedBookings));
        }

        return response;
      } else {
        throw new Error(response.message || 'Failed to submit review');
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      throw error;
    }
  };

  // Check if booking has already been reviewed
  const hasBookingBeenReviewed = (bookingNumber) => {
    if (!bookingNumber) return false;

    const reviewedBookings = JSON.parse(localStorage.getItem('reviewedBookings') || '[]');
    return reviewedBookings.includes(bookingNumber);
  };

  // Auto-trigger review popup based on URL parameters or localStorage
  useEffect(() => {
    // Check if we should show review popup based on URL params
    const urlParams = new URLSearchParams(window.location.search);
    const showReview = urlParams.get('showReview');
    const bookingId = urlParams.get('bookingId');

    if (showReview === 'true' && bookingId) {
      // Check if this booking has already been reviewed
      if (!hasBookingBeenReviewed(bookingId)) {
        // Try to get booking data from localStorage or create minimal data
        const storedBookingData = localStorage.getItem(`booking_${bookingId}`);

        if (storedBookingData) {
          const booking = JSON.parse(storedBookingData);
          triggerReviewPopup(booking);
        } else {
          // Create minimal booking data if not found
          const minimalBooking = {
            bookingNumber: bookingId,
            contactName: '',
            serviceType: 'general'
          };
          triggerReviewPopup(minimalBooking);
        }

        // Clean up URL parameters
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      }
    }
  }, []);

  return {
    isReviewPopupOpen,
    bookingData,
    reviewSubmitted,
    triggerReviewPopup,
    closeReviewPopup,
    handleReviewSubmit,
    hasBookingBeenReviewed
  };
};

export default useReviewPopup;
