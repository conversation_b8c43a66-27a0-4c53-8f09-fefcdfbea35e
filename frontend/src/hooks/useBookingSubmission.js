/**
 * Custom Hook for Booking Submission Logic
 */

import { useState, useCallback } from 'react';
import { submitBooking, generateQuote } from '../services/bookingService';
import {
  showBookingSuccessToast,
  showBookingErrorToast,
  showQuoteSuccessToast,
  showQuoteErrorToast
} from '../utils/toastNotifications';

/**
 * Custom hook for managing booking submission
 * @param {Function} onBookingSuccess - Callback function called after successful booking
 * @returns {Object} Submission state and handlers
 */
export const useBookingSubmission = (onBookingSuccess) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [showPrice, setShowPrice] = useState(false);
  const [quoteData, setQuoteData] = useState(null);

  /**
   * Handle quote generation
   * @param {Object} formData - Form data for quote generation
   * @returns {Promise<boolean>} Success status
   */
  const handleQuoteSubmit = useCallback(async (formData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const result = await generateQuote(formData);

      if (result.success) {
        setQuoteData(result.quote);
        setShowPrice(true);
        showQuoteSuccessToast();
        return true;
      } else {
        throw new Error('Failed to generate quote');
      }
    } catch (error) {
      console.error('Error generating quote:', error);
      const errorMessage = error.message || 'Unable to get quote at this time. Please try again later.';
      setError(errorMessage);
      showQuoteErrorToast(errorMessage);
      return false;
    } finally {
      setIsSubmitting(false);
    }
  }, []);

  /**
   * Handle booking submission
   * @param {Object} formData - Form data for booking submission
   * @param {Function} onSuccess - Callback for successful submission
   * @returns {Promise<boolean>} Success status
   */
  const handleBookingSubmit = useCallback(async (formData, onSuccess) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const result = await submitBooking(formData);

      console.log('Booking saved:', result);
      showBookingSuccessToast();

      // Store booking data for potential review popup
      if (result.data && result.data.bookingNumber) {
        localStorage.setItem(`booking_${result.data.bookingNumber}`, JSON.stringify({
          bookingNumber: result.data.bookingNumber,
          contactName: formData.name,
          contactEmail: formData.email,
          contactPhone: formData.phone,
          serviceType: formData.serviceType,
          address: {
            city: formData.address?.split(',')[0] || '',
            state: 'WA'
          }
        }));
      }

      // Call the booking success callback to trigger review popup
      console.log('📞 Calling onBookingSuccess callback with result:', result.data || result);
      if (onBookingSuccess) {
        onBookingSuccess(result.data || result);
      } else {
        console.log('⚠️ No onBookingSuccess callback provided');
      }

      if (onSuccess) {
        onSuccess(result);
      }

      return true;
    } catch (error) {
      console.error('Error submitting booking:', error);

      showBookingErrorToast();

      // Set detailed error for display in UI
      setError({
        title: "Unable to submit booking at this time.",
        details: error.message,
        fallback: "Please call us directly at (************* to complete your booking.",
        note: "We apologize for any inconvenience."
      });

      return false;
    } finally {
      setIsSubmitting(false);
    }
  }, [onBookingSuccess]);

  /**
   * Reset submission state
   */
  const resetSubmissionState = useCallback(() => {
    setIsSubmitting(false);
    setError(null);
    setShowPrice(false);
    setQuoteData(null);
  }, []);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Go back to form (hide price display)
   */
  const goBackToForm = useCallback(() => {
    setShowPrice(false);
    setError(null);
  }, []);

  /**
   * Check if quote is available
   * @returns {boolean} Whether quote data is available
   */
  const hasQuote = useCallback(() => {
    return Boolean(quoteData);
  }, [quoteData]);

  return {
    // Submission state
    isSubmitting,
    error,
    showPrice,
    quoteData,

    // Submission handlers
    handleQuoteSubmit,
    handleBookingSubmit,

    // State management
    resetSubmissionState,
    clearError,
    goBackToForm,

    // Utilities
    hasQuote,
  };
};
