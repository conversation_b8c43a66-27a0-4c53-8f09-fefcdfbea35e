<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="./src/assets/checkmaid-logo.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover, minimum-scale=1.0" />
    <meta name="theme-color" content="#10b981" />
    <title>Check Maid</title>
    <style>
      html, body {
        overflow-x: hidden;
        position: relative;
        width: 100%;
        -webkit-overflow-scrolling: touch;
        touch-action: pan-y;
        overscroll-behavior: none;
        -webkit-tap-highlight-color: transparent;
        -webkit-text-size-adjust: 100%;
      }
      * {
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
        touch-action: manipulation;
      }
      input, textarea, select {
        -webkit-user-select: text;
        user-select: text;
        touch-action: auto;
      }
    </style>
    <script>
      // Prevent double-tap zoom
      document.addEventListener('touchstart', function(event) {
        if (event.touches.length > 1) {
          event.preventDefault();
        }
      }, { passive: false });

      // Prevent pinch zoom
      document.addEventListener('gesturestart', function(event) {
        event.preventDefault();
      }, { passive: false });

      // Prevent double-tap zoom on iOS
      let lastTouchEnd = 0;
      document.addEventListener('touchend', function(event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, { passive: false });
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
